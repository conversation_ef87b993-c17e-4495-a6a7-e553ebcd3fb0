{"name": "postcss-pseudo-class-any-link", "description": "Use the :any-link pseudo-class in CSS", "version": "8.0.2", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "CC0-1.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "engines": {"node": "^14 || ^16 || >=18"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "dependencies": {"postcss-selector-parser": "^6.0.10"}, "peerDependencies": {"postcss": "^8.4"}, "scripts": {"prebuild": "npm run clean", "build": "rollup -c ../../rollup/default.mjs", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true }); fs.mkdirSync('./dist');\"", "docs": "node ../../.github/bin/generate-docs/install.mjs && node ../../.github/bin/generate-docs/readme.mjs", "lint": "npm run lint:eslint && npm run lint:package-json", "lint:eslint": "eslint ./src --ext .js --ext .ts --ext .mjs --no-error-on-unmatched-pattern", "lint:package-json": "node ../../.github/bin/format-package-json.mjs", "prepublishOnly": "npm run clean && npm run build && npm run test", "test": "node .tape.mjs && npm run test:exports", "test:exports": "node ./test/_import.mjs && node ./test/_require.cjs", "test:rewrite-expects": "REWRITE_EXPECTS=true node .tape.mjs"}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-pseudo-class-any-link#readme", "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-pseudo-class-any-link"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["a", "any-link", "area", "css", "href", "hyperlink", "link", "postcss", "postcss-plugin", "visited"], "csstools": {"cssdbId": "any-link-pseudo-class", "exportName": "postcssPseudoClassAnyLink", "humanReadableName": "PostCSS Pseudo Class Any Link", "specUrl": "https://www.w3.org/TR/selectors-4/#the-any-link-pseudo"}, "volta": {"extends": "../../package.json"}}