{"name": "date-fns", "version": "2.21.1", "sideEffects": false, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "description": "Modern JavaScript date utility library", "repository": "https://github.com/date-fns/date-fns", "funding": {"type": "opencollective", "url": "https://opencollective.com/date-fns"}, "engines": {"node": ">=0.11"}, "typings": "./typings.d.ts", "main": "index.js", "module": "esm/index.js", "scripts": {"test": "karma start config/karma.js", "lint": "eslint .", "lint-types": "eslint --config=.ts-eslintrc.js typings.d.ts", "locale-snapshots": "env TZ=utc babel-node --extensions .ts,.js ./scripts/build/localeSnapshots/index.js", "benchmark": "env TEST_BENCHMARK=true yarn test --single-run", "stats": "cloc . --exclude-dir=node_modules,tmp,.git"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["prettier --write", "git add"]}, "dependencies": {}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-template-literals": "^7.4.4", "@babel/preset-env": "^7.11.5", "@babel/preset-typescript": "^7.10.4", "@date-fns/date-fns-scripts": "0.0.4", "@octokit/core": "^3.2.5", "@types/jest": "^26.0.13", "@types/node": "^14.6.3", "@types/power-assert": "^1.5.3", "@types/sinon": "^9.0.6", "@typescript-eslint/eslint-plugin": "^2.9.0", "@typescript-eslint/parser": "^2.9.0", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "babel-plugin-add-import-extension": "^1.4.3", "babel-plugin-add-module-exports": "^1.0.2", "babel-preset-power-assert": "^3.0.0", "cloc": "^2.2.0", "coveralls": "^3.0.6", "eslint": "^5.16.0", "eslint-config-prettier": "^4.3.0", "firebase": "^3.7.1", "flow-bin": "0.84.0", "fs-promise": "^1.0.0", "glob-promise": "^2.0.0", "gzip-size-cli": "^1.0.0", "husky": "^1.0.1", "istanbul-instrumenter-loader": "^3.0.1", "jest": "^24.8.0", "js-beautify": "^1.5.10", "js-fns": "^2.5.1", "jsdoc-babel": "^0.5.0", "jsdoc-to-markdown": "7", "karma": "^3.1.4", "karma-benchmark": "^1.0.4", "karma-benchmark-reporter": "^0.1.1", "karma-chrome-launcher": "2.2", "karma-cli": "^1.0.1", "karma-coverage": "^1.1.2", "karma-coverage-istanbul-reporter": "^2.1.0", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4", "karma-sauce-launcher": "^1.2.0", "karma-sourcemap-loader": "^0.3.5", "karma-webpack": "^4.0.2", "lint-staged": "^7.3.0", "lodash": "^4.17.15", "lodash.clonedeep": "^4.5.0", "mocha": "^3.5.3", "moment": "^2.24.0", "mz": "^2.7.0", "node-fetch": "^1.7.3", "power-assert": "^1.6.1", "prettier": "2", "rimraf": "^2.7.1", "simple-git": "^2.35.2", "sinon": "^7.4.1", "size-limit": "^0.21.0", "snazzy": "^7.0.0", "ts-node": "^9.1.1", "typescript": "^4.0.2", "webpack": "4", "webpack-cli": "^3.1.2", "world-countries": "^1.8.1"}, "resolutions": {"ajv": "6.8.1"}}