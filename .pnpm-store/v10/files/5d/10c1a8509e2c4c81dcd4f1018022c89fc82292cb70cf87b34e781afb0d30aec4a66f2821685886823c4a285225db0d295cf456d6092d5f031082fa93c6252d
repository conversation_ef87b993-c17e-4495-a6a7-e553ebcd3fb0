.dark, .dark-theme {
  --plum-a1: #f112f108;
  --plum-a2: #f22ff211;
  --plum-a3: #fd4cfd27;
  --plum-a4: #f646ff3a;
  --plum-a5: #f455ff48;
  --plum-a6: #f66dff56;
  --plum-a7: #f07cfd70;
  --plum-a8: #ee84ff95;
  --plum-a9: #e961feb6;
  --plum-a10: #ed70ffc0;
  --plum-a11: #f19cfef3;
  --plum-a12: #feddfef4;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .dark, .dark-theme {
      --plum-a1: color(display-p3 0.973 0.071 0.973 / 0.026);
      --plum-a2: color(display-p3 0.933 0.267 1 / 0.059);
      --plum-a3: color(display-p3 0.918 0.333 0.996 / 0.148);
      --plum-a4: color(display-p3 0.91 0.318 1 / 0.219);
      --plum-a5: color(display-p3 0.914 0.388 1 / 0.269);
      --plum-a6: color(display-p3 0.906 0.463 1 / 0.328);
      --plum-a7: color(display-p3 0.906 0.529 1 / 0.425);
      --plum-a8: color(display-p3 0.906 0.553 1 / 0.568);
      --plum-a9: color(display-p3 0.875 0.427 1 / 0.69);
      --plum-a10: color(display-p3 0.886 0.471 0.996 / 0.732);
      --plum-a11: color(display-p3 0.86 0.602 0.933);
      --plum-a12: color(display-p3 0.936 0.836 0.949);
    }
  }
}