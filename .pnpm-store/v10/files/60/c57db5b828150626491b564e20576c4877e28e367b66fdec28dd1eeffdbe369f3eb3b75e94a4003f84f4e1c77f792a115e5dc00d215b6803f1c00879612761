{"name": "@akryum/tinypool", "version": "0.3.1", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "type": "module", "main": "./dist/esm/index.js", "module": "./dist/esm/index.js", "types": "./dist/index.d.ts", "files": ["dist/**"], "publishConfig": {"access": "public"}, "scripts": {"test:ci": "node --experimental-vm-modules node_modules/jest/bin/jest.js --no-coverage --runInBand", "test:dev": "node --experimental-vm-modules --trace-warnings node_modules/jest/bin/jest.js --watch --no-coverage", "dev": "tsup --watch", "build": "tsup", "publish": "clean-publish", "prepare": "husky install"}, "repository": {"type": "git", "url": "git+https://github.com/aslemammad/tinypool.git"}, "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "devDependencies": {"@swc/core": "^1.2.120", "@swc/jest": "^0.2.15", "@types/concat-stream": "^1.6.1", "@types/jest": "^27.0.3", "@types/node": "^15.0.1", "@typescript-eslint/eslint-plugin": "^4.22.0", "@typescript-eslint/parser": "^4.22.0", "abort-controller": "^3.0.0", "c8": "^7.10.0", "clean-publish": "^3.4.4", "concat-stream": "^2.0.0", "esbuild": "^0.14.5", "esbuild-register": "^3.2.1", "eventemitter-asyncresource": "^1.0.0", "gen-esm-wrapper": "^1.1.1", "husky": "^7.0.4", "jest": "^27.4.5", "nano-staged": "^0.5.0", "prettier": "^2.5.1", "regenerator-runtime": "^0.13.9", "snazzy": "^9.0.0", "tsup": "^5.11.6", "typescript": "4.3.x", "vite": "^2.7.3"}, "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "homepage": "https://github.com/aslemammad/tinypool#readme", "engines": {"node": ">=14.0.0"}, "jest": {"rootDir": ".", "moduleNameMapper": {"^tinypool$": "<rootDir>/dist/esm/index.js"}, "modulePathIgnorePatterns": ["dist"], "extensionsToTreatAsEsm": [".ts"], "testRegex": "test.(js|ts|tsx)$", "coverageDirectory": "./coverage/", "collectCoverage": true, "coverageReporters": ["json", "html", "text", "text-summary"], "collectCoverageFrom": ["src/**/*.{js,ts,tsx}", "tests/**/*.{js,ts,tsx}"], "transform": {"^.+\\.(t|j)sx?$": "@swc/jest"}}}