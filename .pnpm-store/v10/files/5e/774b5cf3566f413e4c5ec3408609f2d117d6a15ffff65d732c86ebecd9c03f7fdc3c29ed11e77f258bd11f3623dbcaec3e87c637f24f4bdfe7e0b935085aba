pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Summerfruit Light
  Author: <PERSON> (http://christop.club/)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme summerfruit-light
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #FFFFFF  Default Background
base01  #E0E0E0  Lighter Background (Used for status bars, line number and folding marks)
base02  #D0D0D0  Selection Background
base03  #B0B0B0  Comments, Invisibles, Line Highlighting
base04  #000000  Dark Foreground (Used for status bars)
base05  #101010  Default Foreground, Caret, Delimiters, Operators
base06  #151515  Light Foreground (Not often used)
base07  #202020  Light Background (Not often used)
base08  #FF0086  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #FD8900  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #ABA800  Classes, Markup Bold, Search Text Background
base0B  #00C918  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #1FAAAA  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #3777E6  Functions, Methods, Attribute IDs, Headings
base0E  #AD00A1  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #CC6633  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #101010;
  background: #FFFFFF
}
.hljs::selection,
.hljs ::selection {
  background-color: #D0D0D0;
  color: #101010
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #B0B0B0 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #B0B0B0
}
/* base04 - #000000 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #000000
}
/* base05 - #101010 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #101010
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #FF0086
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #FD8900
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #ABA800
}
.hljs-strong {
  font-weight: bold;
  color: #ABA800
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #00C918
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #1FAAAA
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #3777E6
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #AD00A1
}
.hljs-emphasis {
  color: #AD00A1;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #CC6633
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}