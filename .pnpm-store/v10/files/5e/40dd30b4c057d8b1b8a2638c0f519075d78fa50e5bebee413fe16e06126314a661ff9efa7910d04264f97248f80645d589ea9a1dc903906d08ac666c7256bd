{"version": 3, "file": "prepareEvent.js", "sources": ["../../../src/utils/prepareEvent.ts"], "sourcesContent": ["import type {\n  CaptureContext,\n  Client,\n  ClientOptions,\n  Event,\n  EventHint,\n  Scope as <PERSON>opeInterface,\n  ScopeContext,\n  <PERSON>ackFrame,\n  StackParser,\n} from '@sentry/types';\nimport { GLOBAL_OBJ, addExceptionMechanism, dateTimestampInSeconds, normalize, truncate, uuid4 } from '@sentry/utils';\n\nimport { DEFAULT_ENVIRONMENT } from '../constants';\nimport { getGlobalScope } from '../currentScopes';\nimport { notifyEventProcessors } from '../eventProcessors';\nimport { Scope } from '../scope';\nimport { applyScopeDataToEvent, mergeScopeData } from './applyScopeDataToEvent';\n\n/**\n * This type makes sure that we get either a CaptureContext, OR an EventHint.\n * It does not allow mixing them, which could lead to unexpected outcomes, e.g. this is disallowed:\n * { user: { id: '123' }, mechanism: { handled: false } }\n */\nexport type ExclusiveEventHintOrCaptureContext =\n  | (CaptureContext & Partial<{ [key in keyof EventHint]: never }>)\n  | (EventHint & Partial<{ [key in keyof ScopeContext]: never }>);\n\n/**\n * Adds common information to events.\n *\n * The information includes release and environment from `options`,\n * breadcrumbs and context (extra, tags and user) from the scope.\n *\n * Information that is already present in the event is never overwritten. For\n * nested objects, such as the context, keys are merged.\n *\n * @param event The original event.\n * @param hint May contain additional information about the original exception.\n * @param scope A scope containing event metadata.\n * @returns A new event with more information.\n * @hidden\n */\nexport function prepareEvent(\n  options: ClientOptions,\n  event: Event,\n  hint: EventHint,\n  scope?: ScopeInterface,\n  client?: Client,\n  isolationScope?: ScopeInterface,\n): PromiseLike<Event | null> {\n  const { normalizeDepth = 3, normalizeMaxBreadth = 1_000 } = options;\n  const prepared: Event = {\n    ...event,\n    event_id: event.event_id || hint.event_id || uuid4(),\n    timestamp: event.timestamp || dateTimestampInSeconds(),\n  };\n  const integrations = hint.integrations || options.integrations.map(i => i.name);\n\n  applyClientOptions(prepared, options);\n  applyIntegrationsMetadata(prepared, integrations);\n\n  if (client) {\n    client.emit('applyFrameMetadata', event);\n  }\n\n  // Only put debug IDs onto frames for error events.\n  if (event.type === undefined) {\n    applyDebugIds(prepared, options.stackParser);\n  }\n\n  // If we have scope given to us, use it as the base for further modifications.\n  // This allows us to prevent unnecessary copying of data if `captureContext` is not provided.\n  const finalScope = getFinalScope(scope, hint.captureContext);\n\n  if (hint.mechanism) {\n    addExceptionMechanism(prepared, hint.mechanism);\n  }\n\n  const clientEventProcessors = client ? client.getEventProcessors() : [];\n\n  // This should be the last thing called, since we want that\n  // {@link Scope.addEventProcessor} gets the finished prepared event.\n  // Merge scope data together\n  const data = getGlobalScope().getScopeData();\n\n  if (isolationScope) {\n    const isolationData = isolationScope.getScopeData();\n    mergeScopeData(data, isolationData);\n  }\n\n  if (finalScope) {\n    const finalScopeData = finalScope.getScopeData();\n    mergeScopeData(data, finalScopeData);\n  }\n\n  const attachments = [...(hint.attachments || []), ...data.attachments];\n  if (attachments.length) {\n    hint.attachments = attachments;\n  }\n\n  applyScopeDataToEvent(prepared, data);\n\n  const eventProcessors = [\n    ...clientEventProcessors,\n    // Run scope event processors _after_ all other processors\n    ...data.eventProcessors,\n  ];\n\n  const result = notifyEventProcessors(eventProcessors, prepared, hint);\n\n  return result.then(evt => {\n    if (evt) {\n      // We apply the debug_meta field only after all event processors have ran, so that if any event processors modified\n      // file names (e.g.the RewriteFrames integration) the filename -> debug ID relationship isn't destroyed.\n      // This should not cause any PII issues, since we're only moving data that is already on the event and not adding\n      // any new data\n      applyDebugMeta(evt);\n    }\n\n    if (typeof normalizeDepth === 'number' && normalizeDepth > 0) {\n      return normalizeEvent(evt, normalizeDepth, normalizeMaxBreadth);\n    }\n    return evt;\n  });\n}\n\n/**\n *  Enhances event using the client configuration.\n *  It takes care of all \"static\" values like environment, release and `dist`,\n *  as well as truncating overly long values.\n * @param event event instance to be enhanced\n */\nfunction applyClientOptions(event: Event, options: ClientOptions): void {\n  const { environment, release, dist, maxValueLength = 250 } = options;\n\n  if (!('environment' in event)) {\n    event.environment = 'environment' in options ? environment : DEFAULT_ENVIRONMENT;\n  }\n\n  if (event.release === undefined && release !== undefined) {\n    event.release = release;\n  }\n\n  if (event.dist === undefined && dist !== undefined) {\n    event.dist = dist;\n  }\n\n  if (event.message) {\n    event.message = truncate(event.message, maxValueLength);\n  }\n\n  const exception = event.exception && event.exception.values && event.exception.values[0];\n  if (exception && exception.value) {\n    exception.value = truncate(exception.value, maxValueLength);\n  }\n\n  const request = event.request;\n  if (request && request.url) {\n    request.url = truncate(request.url, maxValueLength);\n  }\n}\n\nconst debugIdStackParserCache = new WeakMap<StackParser, Map<string, StackFrame[]>>();\n\n/**\n * Puts debug IDs into the stack frames of an error event.\n */\nexport function applyDebugIds(event: Event, stackParser: StackParser): void {\n  const debugIdMap = GLOBAL_OBJ._sentryDebugIds;\n\n  if (!debugIdMap) {\n    return;\n  }\n\n  let debugIdStackFramesCache: Map<string, StackFrame[]>;\n  const cachedDebugIdStackFrameCache = debugIdStackParserCache.get(stackParser);\n  if (cachedDebugIdStackFrameCache) {\n    debugIdStackFramesCache = cachedDebugIdStackFrameCache;\n  } else {\n    debugIdStackFramesCache = new Map<string, StackFrame[]>();\n    debugIdStackParserCache.set(stackParser, debugIdStackFramesCache);\n  }\n\n  // Build a map of filename -> debug_id\n  const filenameDebugIdMap = Object.entries(debugIdMap).reduce<Record<string, string>>(\n    (acc, [debugIdStackTrace, debugIdValue]) => {\n      let parsedStack: StackFrame[];\n      const cachedParsedStack = debugIdStackFramesCache.get(debugIdStackTrace);\n      if (cachedParsedStack) {\n        parsedStack = cachedParsedStack;\n      } else {\n        parsedStack = stackParser(debugIdStackTrace);\n        debugIdStackFramesCache.set(debugIdStackTrace, parsedStack);\n      }\n\n      for (let i = parsedStack.length - 1; i >= 0; i--) {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const stackFrame = parsedStack[i]!;\n        if (stackFrame.filename) {\n          acc[stackFrame.filename] = debugIdValue;\n          break;\n        }\n      }\n      return acc;\n    },\n    {},\n  );\n\n  try {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    event!.exception!.values!.forEach(exception => {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      exception.stacktrace!.frames!.forEach(frame => {\n        if (frame.filename) {\n          frame.debug_id = filenameDebugIdMap[frame.filename];\n        }\n      });\n    });\n  } catch (e) {\n    // To save bundle size we're just try catching here instead of checking for the existence of all the different objects.\n  }\n}\n\n/**\n * Moves debug IDs from the stack frames of an error event into the debug_meta field.\n */\nexport function applyDebugMeta(event: Event): void {\n  // Extract debug IDs and filenames from the stack frames on the event.\n  const filenameDebugIdMap: Record<string, string> = {};\n  try {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    event.exception!.values!.forEach(exception => {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      exception.stacktrace!.frames!.forEach(frame => {\n        if (frame.debug_id) {\n          if (frame.abs_path) {\n            filenameDebugIdMap[frame.abs_path] = frame.debug_id;\n          } else if (frame.filename) {\n            filenameDebugIdMap[frame.filename] = frame.debug_id;\n          }\n          delete frame.debug_id;\n        }\n      });\n    });\n  } catch (e) {\n    // To save bundle size we're just try catching here instead of checking for the existence of all the different objects.\n  }\n\n  if (Object.keys(filenameDebugIdMap).length === 0) {\n    return;\n  }\n\n  // Fill debug_meta information\n  event.debug_meta = event.debug_meta || {};\n  event.debug_meta.images = event.debug_meta.images || [];\n  const images = event.debug_meta.images;\n  Object.entries(filenameDebugIdMap).forEach(([filename, debug_id]) => {\n    images.push({\n      type: 'sourcemap',\n      code_file: filename,\n      debug_id,\n    });\n  });\n}\n\n/**\n * This function adds all used integrations to the SDK info in the event.\n * @param event The event that will be filled with all integrations.\n */\nfunction applyIntegrationsMetadata(event: Event, integrationNames: string[]): void {\n  if (integrationNames.length > 0) {\n    event.sdk = event.sdk || {};\n    event.sdk.integrations = [...(event.sdk.integrations || []), ...integrationNames];\n  }\n}\n\n/**\n * Applies `normalize` function on necessary `Event` attributes to make them safe for serialization.\n * Normalized keys:\n * - `breadcrumbs.data`\n * - `user`\n * - `contexts`\n * - `extra`\n * @param event Event\n * @returns Normalized event\n */\nfunction normalizeEvent(event: Event | null, depth: number, maxBreadth: number): Event | null {\n  if (!event) {\n    return null;\n  }\n\n  const normalized: Event = {\n    ...event,\n    ...(event.breadcrumbs && {\n      breadcrumbs: event.breadcrumbs.map(b => ({\n        ...b,\n        ...(b.data && {\n          data: normalize(b.data, depth, maxBreadth),\n        }),\n      })),\n    }),\n    ...(event.user && {\n      user: normalize(event.user, depth, maxBreadth),\n    }),\n    ...(event.contexts && {\n      contexts: normalize(event.contexts, depth, maxBreadth),\n    }),\n    ...(event.extra && {\n      extra: normalize(event.extra, depth, maxBreadth),\n    }),\n  };\n\n  // event.contexts.trace stores information about a Transaction. Similarly,\n  // event.spans[] stores information about child Spans. Given that a\n  // Transaction is conceptually a Span, normalization should apply to both\n  // Transactions and Spans consistently.\n  // For now the decision is to skip normalization of Transactions and Spans,\n  // so this block overwrites the normalized event to add back the original\n  // Transaction information prior to normalization.\n  if (event.contexts && event.contexts.trace && normalized.contexts) {\n    normalized.contexts.trace = event.contexts.trace;\n\n    // event.contexts.trace.data may contain circular/dangerous data so we need to normalize it\n    if (event.contexts.trace.data) {\n      normalized.contexts.trace.data = normalize(event.contexts.trace.data, depth, maxBreadth);\n    }\n  }\n\n  // event.spans[].data may contain circular/dangerous data so we need to normalize it\n  if (event.spans) {\n    normalized.spans = event.spans.map(span => {\n      return {\n        ...span,\n        ...(span.data && {\n          data: normalize(span.data, depth, maxBreadth),\n        }),\n      };\n    });\n  }\n\n  return normalized;\n}\n\nfunction getFinalScope(\n  scope: ScopeInterface | undefined,\n  captureContext: CaptureContext | undefined,\n): ScopeInterface | undefined {\n  if (!captureContext) {\n    return scope;\n  }\n\n  const finalScope = scope ? scope.clone() : new Scope();\n  finalScope.update(captureContext);\n  return finalScope;\n}\n\n/**\n * Parse either an `EventHint` directly, or convert a `CaptureContext` to an `EventHint`.\n * This is used to allow to update method signatures that used to accept a `CaptureContext` but should now accept an `EventHint`.\n */\nexport function parseEventHintOrCaptureContext(\n  hint: ExclusiveEventHintOrCaptureContext | undefined,\n): EventHint | undefined {\n  if (!hint) {\n    return undefined;\n  }\n\n  // If you pass a Scope or `() => Scope` as CaptureContext, we just return this as captureContext\n  if (hintIsScopeOrFunction(hint)) {\n    return { captureContext: hint };\n  }\n\n  if (hintIsScopeContext(hint)) {\n    return {\n      captureContext: hint,\n    };\n  }\n\n  return hint;\n}\n\nfunction hintIsScopeOrFunction(\n  hint: CaptureContext | EventHint,\n): hint is ScopeInterface | ((scope: ScopeInterface) => ScopeInterface) {\n  return hint instanceof Scope || typeof hint === 'function';\n}\n\ntype ScopeContextProperty = keyof ScopeContext;\nconst captureContextKeys: readonly ScopeContextProperty[] = [\n  'user',\n  'level',\n  'extra',\n  'contexts',\n  'tags',\n  'fingerprint',\n  'requestSession',\n  'propagationContext',\n] as const;\n\nfunction hintIsScopeContext(hint: Partial<ScopeContext> | EventHint): hint is Partial<ScopeContext> {\n  return Object.keys(hint).some(key => captureContextKeys.includes(key as ScopeContextProperty));\n}\n"], "names": ["uuid4", "dateTimestampInSeconds", "addExceptionMechanism", "getGlobalScope", "mergeScopeData", "applyScopeDataToEvent", "eventProcessors", "notifyEventProcessors", "DEFAULT_ENVIRONMENT", "truncate", "GLOBAL_OBJ", "normalize", "scope", "<PERSON><PERSON>"], "mappings": ";;;;;;;;;AAmBA;AACA;AACA;AACA;AACA;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY;AAC5B,EAAE,OAAO;AACT,EAAE,KAAK;AACP,EAAE,IAAI;AACN,EAAE,KAAK;AACP,EAAE,MAAM;AACR,EAAE,cAAc;AAChB,EAA6B;AAC7B,EAAE,MAAM,EAAE,cAAA,GAAiB,CAAC,EAAE,mBAAA,GAAsB,IAAA,EAAQ,GAAE,OAAO,CAAA;AACrE,EAAE,MAAM,QAAQ,GAAU;AAC1B,IAAI,GAAG,KAAK;AACZ,IAAI,QAAQ,EAAE,KAAK,CAAC,QAAS,IAAG,IAAI,CAAC,QAAS,IAAGA,WAAK,EAAE;AACxD,IAAI,SAAS,EAAE,KAAK,CAAC,aAAaC,4BAAsB,EAAE;AAC1D,GAAG,CAAA;AACH,EAAE,MAAM,YAAa,GAAE,IAAI,CAAC,YAAA,IAAgB,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,IAAI,CAAC,CAAA;AACjF;AACA,EAAE,kBAAkB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;AACvC,EAAE,yBAAyB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAA;AACnD;AACA,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAA;AAC5C,GAAE;AACF;AACA;AACA,EAAE,IAAI,KAAK,CAAC,IAAK,KAAI,SAAS,EAAE;AAChC,IAAI,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,WAAW,CAAC,CAAA;AAChD,GAAE;AACF;AACA;AACA;AACA,EAAE,MAAM,UAAW,GAAE,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,CAAA;AAC9D;AACA,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE;AACtB,IAAIC,2BAAqB,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;AACnD,GAAE;AACF;AACA,EAAE,MAAM,qBAAA,GAAwB,MAAA,GAAS,MAAM,CAAC,kBAAkB,EAAC,GAAI,EAAE,CAAA;AACzE;AACA;AACA;AACA;AACA,EAAE,MAAM,OAAOC,4BAAc,EAAE,CAAC,YAAY,EAAE,CAAA;AAC9C;AACA,EAAE,IAAI,cAAc,EAAE;AACtB,IAAI,MAAM,aAAc,GAAE,cAAc,CAAC,YAAY,EAAE,CAAA;AACvD,IAAIC,oCAAc,CAAC,IAAI,EAAE,aAAa,CAAC,CAAA;AACvC,GAAE;AACF;AACA,EAAE,IAAI,UAAU,EAAE;AAClB,IAAI,MAAM,cAAe,GAAE,UAAU,CAAC,YAAY,EAAE,CAAA;AACpD,IAAIA,oCAAc,CAAC,IAAI,EAAE,cAAc,CAAC,CAAA;AACxC,GAAE;AACF;AACA,EAAE,MAAM,WAAY,GAAE,CAAC,IAAI,IAAI,CAAC,WAAA,IAAe,EAAE,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,CAAA;AACxE,EAAE,IAAI,WAAW,CAAC,MAAM,EAAE;AAC1B,IAAI,IAAI,CAAC,WAAY,GAAE,WAAW,CAAA;AAClC,GAAE;AACF;AACA,EAAEC,2CAAqB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;AACvC;AACA,EAAE,MAAMC,oBAAkB;AAC1B,IAAI,GAAG,qBAAqB;AAC5B;AACA,IAAI,GAAG,IAAI,CAAC,eAAe;AAC3B,GAAG,CAAA;AACH;AACA,EAAE,MAAM,MAAO,GAAEC,qCAAqB,CAACD,iBAAe,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;AACvE;AACA,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO;AAC5B,IAAI,IAAI,GAAG,EAAE;AACb;AACA;AACA;AACA;AACA,MAAM,cAAc,CAAC,GAAG,CAAC,CAAA;AACzB,KAAI;AACJ;AACA,IAAI,IAAI,OAAO,cAAe,KAAI,YAAY,cAAA,GAAiB,CAAC,EAAE;AAClE,MAAM,OAAO,cAAc,CAAC,GAAG,EAAE,cAAc,EAAE,mBAAmB,CAAC,CAAA;AACrE,KAAI;AACJ,IAAI,OAAO,GAAG,CAAA;AACd,GAAG,CAAC,CAAA;AACJ,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,kBAAkB,CAAC,KAAK,EAAS,OAAO,EAAuB;AACxE,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,cAAe,GAAE,GAAI,EAAA,GAAI,OAAO,CAAA;AACtE;AACA,EAAE,IAAI,EAAE,iBAAiB,KAAK,CAAC,EAAE;AACjC,IAAI,KAAK,CAAC,WAAA,GAAc,aAAA,IAAiB,OAAQ,GAAE,WAAY,GAAEE,6BAAmB,CAAA;AACpF,GAAE;AACF;AACA,EAAE,IAAI,KAAK,CAAC,OAAA,KAAY,SAAA,IAAa,OAAA,KAAY,SAAS,EAAE;AAC5D,IAAI,KAAK,CAAC,OAAQ,GAAE,OAAO,CAAA;AAC3B,GAAE;AACF;AACA,EAAE,IAAI,KAAK,CAAC,IAAA,KAAS,SAAA,IAAa,IAAA,KAAS,SAAS,EAAE;AACtD,IAAI,KAAK,CAAC,IAAK,GAAE,IAAI,CAAA;AACrB,GAAE;AACF;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,KAAK,CAAC,OAAA,GAAUC,cAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,cAAc,CAAC,CAAA;AAC3D,GAAE;AACF;AACA,EAAE,MAAM,YAAY,KAAK,CAAC,SAAU,IAAG,KAAK,CAAC,SAAS,CAAC,MAAO,IAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;AAC1F,EAAE,IAAI,SAAA,IAAa,SAAS,CAAC,KAAK,EAAE;AACpC,IAAI,SAAS,CAAC,KAAA,GAAQA,cAAQ,CAAC,SAAS,CAAC,KAAK,EAAE,cAAc,CAAC,CAAA;AAC/D,GAAE;AACF;AACA,EAAE,MAAM,OAAA,GAAU,KAAK,CAAC,OAAO,CAAA;AAC/B,EAAE,IAAI,OAAA,IAAW,OAAO,CAAC,GAAG,EAAE;AAC9B,IAAI,OAAO,CAAC,GAAA,GAAMA,cAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,cAAc,CAAC,CAAA;AACvD,GAAE;AACF,CAAA;AACA;AACA,MAAM,uBAAwB,GAAE,IAAI,OAAO,EAA0C,CAAA;AACrF;AACA;AACA;AACA;AACO,SAAS,aAAa,CAAC,KAAK,EAAS,WAAW,EAAqB;AAC5E,EAAE,MAAM,UAAA,GAAaC,gBAAU,CAAC,eAAe,CAAA;AAC/C;AACA,EAAE,IAAI,CAAC,UAAU,EAAE;AACnB,IAAI,OAAM;AACV,GAAE;AACF;AACA,EAAE,IAAI,uBAAuB,CAAA;AAC7B,EAAE,MAAM,+BAA+B,uBAAuB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;AAC/E,EAAE,IAAI,4BAA4B,EAAE;AACpC,IAAI,uBAAA,GAA0B,4BAA4B,CAAA;AAC1D,SAAS;AACT,IAAI,uBAAwB,GAAE,IAAI,GAAG,EAAwB,CAAA;AAC7D,IAAI,uBAAuB,CAAC,GAAG,CAAC,WAAW,EAAE,uBAAuB,CAAC,CAAA;AACrE,GAAE;AACF;AACA;AACA,EAAE,MAAM,kBAAmB,GAAE,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,MAAM;AAC9D,IAAI,CAAC,GAAG,EAAE,CAAC,iBAAiB,EAAE,YAAY,CAAC,KAAK;AAChD,MAAM,IAAI,WAAW,CAAA;AACrB,MAAM,MAAM,oBAAoB,uBAAuB,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAA;AAC9E,MAAM,IAAI,iBAAiB,EAAE;AAC7B,QAAQ,WAAA,GAAc,iBAAiB,CAAA;AACvC,aAAa;AACb,QAAQ,WAAY,GAAE,WAAW,CAAC,iBAAiB,CAAC,CAAA;AACpD,QAAQ,uBAAuB,CAAC,GAAG,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAA;AACnE,OAAM;AACN;AACA,MAAM,KAAK,IAAI,CAAE,GAAE,WAAW,CAAC,MAAA,GAAS,CAAC,EAAE,CAAE,IAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACxD;AACA,QAAQ,MAAM,UAAW,GAAE,WAAW,CAAC,CAAC,CAAC,CAAA;AACzC,QAAQ,IAAI,UAAU,CAAC,QAAQ,EAAE;AACjC,UAAU,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAA,GAAI,YAAY,CAAA;AACjD,UAAU,MAAK;AACf,SAAQ;AACR,OAAM;AACN,MAAM,OAAO,GAAG,CAAA;AAChB,KAAK;AACL,IAAI,EAAE;AACN,GAAG,CAAA;AACH;AACA,EAAE,IAAI;AACN;AACA,IAAI,KAAK,CAAE,SAAS,CAAE,MAAM,CAAE,OAAO,CAAC,SAAA,IAAa;AACnD;AACA,MAAM,SAAS,CAAC,UAAU,CAAE,MAAM,CAAE,OAAO,CAAC,KAAA,IAAS;AACrD,QAAQ,IAAI,KAAK,CAAC,QAAQ,EAAE;AAC5B,UAAU,KAAK,CAAC,QAAS,GAAE,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;AAC7D,SAAQ;AACR,OAAO,CAAC,CAAA;AACR,KAAK,CAAC,CAAA;AACN,GAAI,CAAA,OAAO,CAAC,EAAE;AACd;AACA,GAAE;AACF,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,KAAK,EAAe;AACnD;AACA,EAAE,MAAM,kBAAkB,GAA2B,EAAE,CAAA;AACvD,EAAE,IAAI;AACN;AACA,IAAI,KAAK,CAAC,SAAS,CAAE,MAAM,CAAE,OAAO,CAAC,SAAA,IAAa;AAClD;AACA,MAAM,SAAS,CAAC,UAAU,CAAE,MAAM,CAAE,OAAO,CAAC,KAAA,IAAS;AACrD,QAAQ,IAAI,KAAK,CAAC,QAAQ,EAAE;AAC5B,UAAU,IAAI,KAAK,CAAC,QAAQ,EAAE;AAC9B,YAAY,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAE,GAAE,KAAK,CAAC,QAAQ,CAAA;AAC/D,iBAAiB,IAAI,KAAK,CAAC,QAAQ,EAAE;AACrC,YAAY,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAE,GAAE,KAAK,CAAC,QAAQ,CAAA;AAC/D,WAAU;AACV,UAAU,OAAO,KAAK,CAAC,QAAQ,CAAA;AAC/B,SAAQ;AACR,OAAO,CAAC,CAAA;AACR,KAAK,CAAC,CAAA;AACN,GAAI,CAAA,OAAO,CAAC,EAAE;AACd;AACA,GAAE;AACF;AACA,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,MAAA,KAAW,CAAC,EAAE;AACpD,IAAI,OAAM;AACV,GAAE;AACF;AACA;AACA,EAAE,KAAK,CAAC,UAAW,GAAE,KAAK,CAAC,UAAA,IAAc,EAAE,CAAA;AAC3C,EAAE,KAAK,CAAC,UAAU,CAAC,MAAO,GAAE,KAAK,CAAC,UAAU,CAAC,MAAO,IAAG,EAAE,CAAA;AACzD,EAAE,MAAM,MAAO,GAAE,KAAK,CAAC,UAAU,CAAC,MAAM,CAAA;AACxC,EAAE,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,KAAK;AACvE,IAAI,MAAM,CAAC,IAAI,CAAC;AAChB,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,SAAS,EAAE,QAAQ;AACzB,MAAM,QAAQ;AACd,KAAK,CAAC,CAAA;AACN,GAAG,CAAC,CAAA;AACJ,CAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,yBAAyB,CAAC,KAAK,EAAS,gBAAgB,EAAkB;AACnF,EAAE,IAAI,gBAAgB,CAAC,MAAO,GAAE,CAAC,EAAE;AACnC,IAAI,KAAK,CAAC,GAAI,GAAE,KAAK,CAAC,GAAA,IAAO,EAAE,CAAA;AAC/B,IAAI,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,gBAAgB,EAAE,GAAG,GAAG,gBAAgB,CAAC,CAAA;AACrF,GAAE;AACF,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,cAAc,CAAC,KAAK,EAAgB,KAAK,EAAU,UAAU,EAAwB;AAC9F,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA,EAAE,MAAM,UAAU,GAAU;AAC5B,IAAI,GAAG,KAAK;AACZ,IAAI,IAAI,KAAK,CAAC,eAAe;AAC7B,MAAM,WAAW,EAAE,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAE,KAAI;AAC/C,QAAQ,GAAG,CAAC;AACZ,QAAQ,IAAI,CAAC,CAAC,QAAQ;AACtB,UAAU,IAAI,EAAEC,eAAS,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC;AACpD,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,QAAQ;AACtB,MAAM,IAAI,EAAEA,eAAS,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC;AACpD,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,YAAY;AAC1B,MAAM,QAAQ,EAAEA,eAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,UAAU,CAAC;AAC5D,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,SAAS;AACvB,MAAM,KAAK,EAAEA,eAAS,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC;AACtD,KAAK;AACL,GAAG,CAAA;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,KAAK,CAAC,YAAY,KAAK,CAAC,QAAQ,CAAC,KAAM,IAAG,UAAU,CAAC,QAAQ,EAAE;AACrE,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAA,GAAQ,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAA;AACpD;AACA;AACA,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;AACnC,MAAM,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAA,GAAOA,eAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;AAC9F,KAAI;AACJ,GAAE;AACF;AACA;AACA,EAAE,IAAI,KAAK,CAAC,KAAK,EAAE;AACnB,IAAI,UAAU,CAAC,KAAA,GAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,IAAA,IAAQ;AAC/C,MAAM,OAAO;AACb,QAAQ,GAAG,IAAI;AACf,QAAQ,IAAI,IAAI,CAAC,QAAQ;AACzB,UAAU,IAAI,EAAEA,eAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC;AACvD,SAAS;AACT,OAAO,CAAA;AACP,KAAK,CAAC,CAAA;AACN,GAAE;AACF;AACA,EAAE,OAAO,UAAU,CAAA;AACnB,CAAA;AACA;AACA,SAAS,aAAa;AACtB,EAAEC,OAAK;AACP,EAAE,cAAc;AAChB,EAA8B;AAC9B,EAAE,IAAI,CAAC,cAAc,EAAE;AACvB,IAAI,OAAOA,OAAK,CAAA;AAChB,GAAE;AACF;AACA,EAAE,MAAM,UAAA,GAAaA,OAAA,GAAQA,OAAK,CAAC,KAAK,EAAG,GAAE,IAAIC,WAAK,EAAE,CAAA;AACxD,EAAE,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,CAAA;AACnC,EAAE,OAAO,UAAU,CAAA;AACnB,CAAA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,8BAA8B;AAC9C,EAAE,IAAI;AACN,EAAyB;AACzB,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,SAAS,CAAA;AACpB,GAAE;AACF;AACA;AACA,EAAE,IAAI,qBAAqB,CAAC,IAAI,CAAC,EAAE;AACnC,IAAI,OAAO,EAAE,cAAc,EAAE,MAAM,CAAA;AACnC,GAAE;AACF;AACA,EAAE,IAAI,kBAAkB,CAAC,IAAI,CAAC,EAAE;AAChC,IAAI,OAAO;AACX,MAAM,cAAc,EAAE,IAAI;AAC1B,KAAK,CAAA;AACL,GAAE;AACF;AACA,EAAE,OAAO,IAAI,CAAA;AACb,CAAA;AACA;AACA,SAAS,qBAAqB;AAC9B,EAAE,IAAI;AACN,EAAwE;AACxE,EAAE,OAAO,gBAAgBA,WAAA,IAAS,OAAO,IAAA,KAAS,UAAU,CAAA;AAC5D,CAAA;;AAGA,MAAM,kBAAkB,GAAoC;AAC5D,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAAE,OAAO;AACT,EAAE,UAAU;AACZ,EAAE,MAAM;AACR,EAAE,aAAa;AACf,EAAE,gBAAgB;AAClB,EAAE,oBAAoB;AACtB,CAAE,EAAA;AACF;AACA,SAAS,kBAAkB,CAAC,IAAI,EAAoE;AACpG,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAA,IAAO,kBAAkB,CAAC,QAAQ,CAAC,GAAA,EAA4B,CAAC,CAAA;AAChG;;;;;;;"}