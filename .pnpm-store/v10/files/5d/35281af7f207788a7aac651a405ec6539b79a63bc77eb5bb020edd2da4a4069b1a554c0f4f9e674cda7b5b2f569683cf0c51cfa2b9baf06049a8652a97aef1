{"version": 3, "file": "ajs-destination.bundle.b0d83c7f45c362bb9dcf.js", "mappings": "uIASO,SAASA,EAAcC,GAC1B,MAA2E,WAAnEC,OAAOC,UAAUC,SAASC,KAAKJ,GAAKK,MAAM,GAAI,GAAGC,c,uDCFtD,SAASC,EACdC,EACAC,G,QAGA,MAAkC,kBAAvBA,MAAAA,OAAS,EAATA,EAAWC,SACbD,EAAUC,QAIY,QAAxB,EAAe,QAAf,EAAAF,MAAAA,OAAI,EAAJA,EAAMG,iBAAS,eAAED,eAAO,S,uSCNjC,SAASE,EAAcC,GACrB,OAAOA,EAAKP,cAAcQ,QAAQ,IAAK,IAAIA,QAAQ,OAAQ,KAG7D,SAASC,EAAkBC,EAAkBC,GAC3C,YAD2C,IAAAA,IAAAA,GAAA,GACpCA,EAAYC,KAAKF,GAAUF,QAAQ,KAAM,SAAMK,EAoDjD,SAAeC,EACpBC,EACAR,EACAS,EACAL,G,2BACCM,SAAO,W,wEACFP,EAAWJ,EAAcC,GACzBW,EAAqBT,EAAkBC,EAAUC,GACjDQ,GAAO,UAEPC,EAAW,UAAGD,EAAI,yBACtBD,MAAAA,EAAAA,EAAsBR,EAAQ,YAC5BM,EAAO,YAAIE,MAAAA,EAAAA,EAAsBR,EAAQ,kB,iBAG3C,O,sBAAA,IAAM,OAAWU,I,cAAjB,SAtDJ,SAA2BA,EAAkBL,EAAcR,G,QACzD,IACS,IAAAc,GACsD,QAA3D,EAAmB,QAAnB,EAAM,OAANC,aAAM,IAANA,YAAM,EAANA,OAAQC,mBAAW,eAAEC,iBAAiBJ,EAAU,mBAAW,QAAI,IAAE,GAEnEC,GACEN,EAAIU,MAAMC,MAAM,0BAA2BC,KAAKC,MAAMP,EAAOQ,WAAW,SACtEtB,GACIc,EAAOQ,SAAW,IAAM,CAAC,UAAY,IAAG,IAEhD,MAAOC,KA6CPC,CAAkBX,EAAUL,EAAKR,G,aAGjC,M,WADAQ,EAAIU,MAAMC,MAAM,2BAA4B,EAAG,CAAC,iBAAUnB,GAAQ,WAC5D,E,OAKR,OADMyB,EAAiBV,OAAO,UAAGZ,EAAQ,SACzC,GAAMO,QAAQgB,IAAID,EAAKE,KAAI,SAACC,GAAQ,cAAWhB,EAAOgB,EAAM,Y,OAK5D,OALA,SAGAb,OAAO,UAAGZ,EAAQ,aAEX,CAAP,EAAOY,OAEL,UAAGZ,EAAQ,wB,cClEf,SAAe0B,EACbC,EACAC,G,2BACCrB,SAAO,W,qEAGR,OAFMsB,EAAyB,IAE3B,SACK,CAAP,EAAOD,GAGT,IAAM,QACJ,WAAM,OAAAA,EAAME,OAAS,IAAK,YAC1B,sD,gEAEE,OADMzB,EAAMuB,EAAMG,OAKH,IAAM,IAAAC,GAAQ3B,EAAKsB,IAHhC,I,cAGIM,EAAS,SACCA,aAAkB,KAEhCJ,EAAYK,KAAK7B,G,sBAOvB,OAlBA,SAiBAwB,EAAYL,KAAI,SAACW,GAAW,OAAAP,EAAMQ,gBAAgBD,MAC3C,CAAP,EAAOP,UAGT,iBAoBE,WACE/B,EACAS,EACA+B,EACAC,EACAC,QAFA,IAAAF,IAAAA,EAAA,IAnBF,KAAAC,QAAuB,GACvB,KAAAE,KAAuB,cACvB,KAAAC,WAA8C,GAEtC,KAAAC,QAAS,EACT,KAAAC,cAAe,EASvB,KAAAC,UAAW,EASTC,KAAKhD,KAAOA,EACZgD,KAAKvC,QAAUA,EACfuC,KAAKR,UAAW,WAAKA,GACrBQ,KAAKC,yBAA2BR,EAAQQ,2BAA4B,EACpED,KAAKN,kBAAoBA,EAIrBM,KAAKR,SAAe,MAA+B,YAA1BQ,KAAKR,SAAe,aACxCQ,KAAKR,SAAe,KAG7BQ,KAAKP,QAAUA,EACfO,KAAKE,OAAST,EAAQU,yBAClB,IAAI,IAAc,EAAG,IACrB,IAAI,IAAuB,EAAG,eAAQnD,IAE1CgD,KAAKI,gBAgNT,OA7ME,YAAAC,SAAA,WACE,OAAOL,KAAKH,QAGd,YAAAS,MAAA,W,MACE,OAAmB,QAAZ,EAAAN,KAAKO,eAAO,QAAI7C,QAAQ8C,WAG3B,YAAAC,KAAN,SAAWjD,EAAckD,G,iCAA+BhD,SAAO,W,uEAC7D,OAAIsC,KAAKH,aAA2BvC,IAAjB0C,KAAKO,QACtB,IAIsB,Q,EAAtBP,KAAKN,yBAAiB,a,kBACrB,SAAMnC,EACLC,EACAwC,KAAKhD,KACLgD,KAAKvC,QACLuC,KAAKP,QAAQrC,Y,OAJf,EAAC,S,iBAFGsC,EAAiB,EASvBM,KAAKW,YDzFF,SACLjB,EACAkB,EACAF,GAEA,IAAIG,EAEA,gBAAiBnB,GAMnBA,EALsB,CACpBoB,KAAM,WAAY,OAAAJ,EAAkBI,QACpCC,eAAgB,eAIlBF,EAAiBnB,EAAkBsB,aAEnCH,EAAiBnB,EAGnB,IAAMiB,EAAc,IAAIE,EAAeD,GAEvC,OADAD,EAAYM,UAAYP,EACjBC,ECoEcO,CACjBxB,EACAM,KAAKR,SACLkB,GAGFV,KAAKO,QAAU,IAAI7C,SAAQ,SAAC8C,GAM1B,EAAKG,YAAaQ,KAAK,SALL,WAChB,EAAKtB,QAAS,EACdW,GAAQ,SAMZR,KAAKoB,aAAe,IAAI1D,SAAQ,SAAC8C,GAM/B,EAAKG,YAAaU,GAAG,cALN,WACb,EAAKvB,cAAe,EACpBU,GAAQ,SAMZ,IACEhD,EAAIU,MAAMoD,UAAU,kCAAmC,EAAG,CACxD,oBACA,2BAAoBtB,KAAKhD,QAG3BgD,KAAKW,YAAYY,aACjB,MAAOC,GAMP,MALAhE,EAAIU,MAAMoD,UAAU,wCAAyC,EAAG,CAC9D,oBACA,2BAAoBtB,KAAKhD,QAGrBwE,E,kBAIV,YAAAC,OAAA,SAAOC,EAAeC,GACpB,ODxEG,SACL3E,EACAS,EACAL,G,2BACCM,SAAO,W,6CASR,OARME,GAAO,UACPT,EAAWJ,EAAcC,GACzBW,EAAqBT,EAAkBF,EAAMI,GAE7CS,EAAW,UAAGD,EAAI,yBACtBD,MAAAA,EAAAA,EAAsBR,EAAQ,YAC5BM,EAAO,YAAIE,MAAAA,EAAAA,EAAsBR,EAAQ,kBAEtC,CAAP,GAAO,OAAaU,UC2DX+D,CAAkB5B,KAAKhD,KAAMgD,KAAKvC,QAASuC,KAAKP,QAAQrC,YAGjE,YAAAyE,cAAA,W,UAAc,kDACZ7B,KAAKJ,YAAa,EAAAI,KAAKJ,YAAWkC,OAAM,QAAIC,IAG9C,YAAAC,aAAA,SAAaxE,GACX,MAEqB,SAAnBA,EAAIyE,MAAMtC,QACT,WAA+B,IAAhBK,KAAKH,SAA0C,IAAtBG,KAAKF,eAIpC,YAAAoC,KAAd,SACE1E,EACA2E,EACAC,G,mCACC1E,SAAO,W,wEACR,GAAIsC,KAAKgC,aAAaxE,GAGpB,OAFAwC,KAAKE,OAAOb,KAAK7B,GACjBwC,KAAKI,gBACE,CAAP,EAAO5C,GAMT,GAHMb,EAAyB,QAAlB,EAAY,QAAZ,EAAAqD,KAAKP,eAAO,eAAE9C,YAAI,eAAE0F,MAC3BC,EAAK9E,EAAIyE,MAAMA,MAEjBtF,GAAQ2F,GAAoB,YAAdtC,KAAKhD,KAAoB,CAGzC,GADMJ,EAAYD,EAAK2F,KAClB,OAAmB3F,EAAMC,GAa5B,OAZAY,EAAI+E,YAAY,gBAAgB,oBAC3B/E,EAAIyE,MAAMO,cAAY,CACzBC,KAAK,EACL,WAAW,KAEbjF,EAAIkF,OACF,IAAI,IAAmB,CACrBC,OAAO,EACPC,OAAQ,gBAASN,EAAE,qCAA6BtC,KAAKhD,KAAI,qBACzD2C,KAAM,qBAGH,CAAP,EAAOnC,GAQT,GANEA,EAAI+E,YAAY,gBAAgB,oBAC3B/E,EAAIyE,MAAMO,cACV5F,MAAAA,OAAS,EAATA,EAAW4F,gBAId5F,MAAAA,OAAS,EAATA,EAAWC,WAAmD,KAAxCD,MAAAA,OAAS,EAATA,EAAW4F,aAAcxC,KAAKhD,OAQtD,OAPAQ,EAAIkF,OACF,IAAI,IAAmB,CACrBC,OAAO,EACPC,OAAQ,gBAASN,EAAE,qCAA6BtC,KAAKhD,KAAI,qBACzD2C,KAAM,qBAGH,CAAP,EAAOnC,GAIa,UAAM,IAAAqF,4BAC5B7C,KAAKhD,KACLQ,EAAIyE,MACJjC,KAAKJ,a,OAGP,GAAwB,QANlBkD,EAAkB,UAOtB,MAAO,CAAP,EAAOtF,GAGHyE,EAAQ,IAAIE,EAAIW,EAAiB,CACrCC,UAAW/C,KAAKC,2BAGlBzC,EAAIU,MAAMoD,UAAU,kCAAmC,EAAG,CACxD,iBAAUc,GACV,2BAAoBpC,KAAKhD,Q,8CAIrBgD,KAAKW,YACP,GAAMX,KAAKW,YAAYqC,OAAOzG,KAAKyD,KAAKW,YAAayB,EAAWH,IAD9D,M,OACF,S,oCAOF,M,WAJAzE,EAAIU,MAAMoD,UAAU,wCAAyC,EAAG,CAC9D,iBAAUc,GACV,2BAAoBpC,KAAKhD,QAErB,E,OAGR,MAAO,CAAP,EAAOQ,WAGH,YAAA6E,MAAN,SAAY7E,G,2BAAeE,SAAO,W,iCAChC,MAAO,CAAP,EAAOsC,KAAKkC,KAAK1E,EAAK,EAAAyF,MAA2B,iBAG7C,YAAAC,KAAN,SAAW1F,G,iCAAeE,SAAO,W,4CAK/B,OAJoB,QAAhB,EAAAsC,KAAKW,mBAAW,eAAEwC,oBAAqBnD,KAAKF,cAC9CE,KAAKW,YAAYY,aAGZ,CAAP,EAAOvB,KAAKoB,aAAcgC,MAAK,WAC7B,OAAO,EAAKlB,KAAK1E,EAAK,EAAA6F,KAAyB,mBAI7C,YAAAC,SAAN,SAAe9F,G,2BAAeE,SAAO,W,iCACnC,MAAO,CAAP,EAAOsC,KAAKkC,KAAK1E,EAAK,EAAA+F,SAAiC,oBAGnD,YAAAC,MAAN,SAAYhG,G,2BAAeE,SAAO,W,iCAChC,MAAO,CAAP,EAAOsC,KAAKkC,KAAK1E,EAAK,EAAAiG,MAA2B,iBAG7C,YAAAC,MAAN,SAAYlG,G,2BAAeE,SAAO,W,iCAChC,MAAO,CAAP,EAAOsC,KAAKkC,KAAK1E,EAAK,EAAAmG,MAA2B,iBAG3C,YAAAvD,cAAR,sBACMJ,KAAKD,UAKT6D,YAAW,sD,8DAEK,OADd5D,KAAKD,UAAW,EAChB,EAAAC,KAAc,GAAMnB,EAAWmB,KAAMA,KAAKE,S,cAA1C,EAAKA,OAAS,SACdF,KAAKD,UAAW,EAEZC,KAAKE,OAAO2D,KAAO,GACrB7D,KAAKI,gB,aAEU,IAAhBhC,KAAK0F,WAEZ,EA5PA,GA8PO,SAASC,EACdvE,EACAwE,EACAvE,EACAwE,EACAC,G,QAEA,QALA,IAAAF,IAAAA,EAAA,SACA,IAAAvE,IAAAA,EAAA,KAII,SACF,MAAO,GAGLD,EAAS7C,QACX8C,EAAUA,MAAAA,EAAAA,EAAW,IACb9C,KAAO6C,EAAS7C,MAG1B,IAAMwH,EAAwD,QAAzC,EAA2B,QAA3B,EAAA3E,EAAS4E,0BAAkB,eAAED,oBAAY,QAAI,GAC5DE,EAA2B7E,EAASgD,aACpC8B,EAA0B7E,EAAQ+C,aAElC+B,GAAqB,OAAc/E,EAAUC,MAAAA,EAAAA,EAAW,IAKxD+E,EAA0BN,MAAAA,OAAwB,EAAxBA,EAA0BO,QACxD,SAACC,EAAKhF,G,MAAsB,OAAC,oBACxBgF,KAAG,MDtUL,SACLhF,GAEA,OACE,gBAAiBA,EACbA,EAAkBsB,YAClBtB,GACJrD,UAAUW,KCgUP2H,CAAiCjF,IAAqBA,EAAiB,MAE1E,IAGIkF,EAA0B,IAAIC,KAAI,oBAEnCzI,OAAO0I,KAAKT,GAA0BU,QAAO,SAAC/H,GAC/C,OChWkC,SACtCA,EACA4D,G,MAEQjB,EAA0CiB,EAAmB,KAAvDoE,EAAoCpE,EAAmB,eAAvCqE,EAAoBrE,EAAmB,gBAG/DsE,EACe,cAAnBF,IACU,YAATrF,IAAqD,QAA/B,EAAAsF,MAAAA,OAAe,EAAfA,EAAiBE,sBAAc,eAAEC,SAAS,aAKnE,OAAQpI,EAAKqI,WAAW,YAAuB,aAATrI,GAAuBkI,EDkVzDI,CAAyBtI,EAAMqH,EAAyBrH,QACzD,GAGEZ,OAAO0I,KAAKN,GAA2B,IAAIO,QAC5C,SAAC/H,GACC,eAAcqH,EAAyBrH,MACvC,QAAcsH,MAAAA,OAAuB,EAAvBA,EAA0BtH,QAC3C,IAGH,OAAOuI,MAAMC,KAAKZ,GACfG,QAAO,SAAC/H,GAAS,OC3Ve,SACnCyI,EACAzB,GAEA,IAAM0B,GACuB,IAA3B1B,EAAmBvB,UACqBnF,IAAxC0G,EAAmByB,GAErB,OAC0C,IAAxCzB,EAAmByB,IAA8BC,EDkV9B,CAAsB1I,EAAMgH,MAC9CrF,KAAI,SAAC3B,GACJ,IACMS,ED1PL,SACL+B,G,YAEA,OAEoC,QADlC,EAAmC,QAAnC,EAAyB,QAAzB,EAAAA,MAAAA,OAAQ,EAARA,EAAUyF,uBAAe,eAAEU,gBAAQ,QACV,QAAzB,EAAAnG,MAAAA,OAAQ,EAARA,EAAUyF,uBAAe,eAAExH,eAAO,QAClC,SCoPkBmI,CADYvB,EAAyBrH,IAE/C6I,EAAc,IAAIC,EACtB9I,EACAS,EACA8G,EAAmBvH,GACnByC,EACA+E,MAAAA,OAAuB,EAAvBA,EAA0BxH,IAU5B,OAPgBmH,EAAaY,QAC3B,SAACgB,GAAS,OAAAA,EAAKC,kBAAoBhJ,KAEzBiC,OAAS,GAAKgF,GACxB4B,EAAYhE,cAAcoC,GAGrB4B", "sources": ["webpack://@june-so/analytics-next/./node_modules/@segment/analytics-core/dist/esm/validation/helpers.js", "webpack://@june-so/analytics-next/./src/lib/is-plan-event-enabled.ts", "webpack://@june-so/analytics-next/./src/plugins/ajs-destination/loader.ts", "webpack://@june-so/analytics-next/./src/plugins/ajs-destination/index.ts", "webpack://@june-so/analytics-next/./src/plugins/ajs-destination/utils.ts"], "sourcesContent": ["export function isString(obj) {\n    return typeof obj === 'string';\n}\nexport function isNumber(obj) {\n    return typeof obj === 'number';\n}\nexport function isFunction(obj) {\n    return typeof obj === 'function';\n}\nexport function isPlainObject(obj) {\n    return (Object.prototype.toString.call(obj).slice(8, -1).toLowerCase() === 'object');\n}\nexport function hasUser(event) {\n    var _a, _b, _c;\n    var id = (_c = (_b = (_a = event.userId) !== null && _a !== void 0 ? _a : event.anonymousId) !== null && _b !== void 0 ? _b : event.groupId) !== null && _c !== void 0 ? _c : event.previousId;\n    return isString(id);\n}\n//# sourceMappingURL=helpers.js.map", "import { PlanEvent, TrackPlan } from '../core/events/interfaces'\n\n/**\n * Determines whether a track event is allowed to be sent based on the\n * user's tracking plan.\n * If the user does not have a tracking plan or the event is allowed based\n * on the tracking plan configuration, returns true.\n */\nexport function isPlanEventEnabled(\n  plan: TrackPlan | undefined,\n  planEvent: PlanEvent | undefined\n): boolean {\n  // Always prioritize the event's `enabled` status\n  if (typeof planEvent?.enabled === 'boolean') {\n    return planEvent.enabled\n  }\n\n  // Assume absence of a tracking plan means events are enabled\n  return plan?.__default?.enabled ?? true\n}\n", "import { Analytics } from '../../core/analytics'\nimport { LegacyIntegrationConfiguration } from '../../browser'\nimport { getNextIntegrationsURL } from '../../lib/parse-cdn'\nimport { Context } from '../../core/context'\nimport { User } from '../../core/user'\nimport { loadScript, unloadScript } from '../../lib/load-script'\nimport {\n  LegacyIntegration,\n  ClassicIntegrationBuilder,\n  ClassicIntegrationSource,\n} from './types'\n\nfunction normalizeName(name: string): string {\n  return name.toLowerCase().replace('.', '').replace(/\\s+/g, '-')\n}\n\nfunction obfuscatePathName(pathName: string, obfuscate = false): string | void {\n  return obfuscate ? btoa(pathName).replace(/=/g, '') : undefined\n}\n\nexport function resolveIntegrationNameFromSource(\n  integrationSource: ClassicIntegrationSource\n) {\n  return (\n    'Integration' in integrationSource\n      ? integrationSource.Integration\n      : integrationSource\n  ).prototype.name\n}\n\nfunction recordLoadMetrics(fullPath: string, ctx: Context, name: string): void {\n  try {\n    const [metric] =\n      window?.performance?.getEntriesByName(fullPath, 'resource') ?? []\n    // we assume everything that took under 100ms is cached\n    metric &&\n      ctx.stats.gauge('legacy_destination_time', Math.round(metric.duration), [\n        name,\n        ...(metric.duration < 100 ? ['cached'] : []),\n      ])\n  } catch (_) {\n    // not available\n  }\n}\n\nexport function buildIntegration(\n  integrationSource: ClassicIntegrationSource,\n  integrationSettings: { [key: string]: any },\n  analyticsInstance: Analytics\n): LegacyIntegration {\n  let integrationCtr: ClassicIntegrationBuilder\n  // GA and Appcues use a different interface to instantiating integrations\n  if ('Integration' in integrationSource) {\n    const analyticsStub = {\n      user: (): User => analyticsInstance.user(),\n      addIntegration: (): void => {},\n    }\n\n    integrationSource(analyticsStub)\n    integrationCtr = integrationSource.Integration\n  } else {\n    integrationCtr = integrationSource\n  }\n\n  const integration = new integrationCtr(integrationSettings)\n  integration.analytics = analyticsInstance\n  return integration\n}\n\nexport async function loadIntegration(\n  ctx: Context,\n  name: string,\n  version: string,\n  obfuscate?: boolean\n): Promise<ClassicIntegrationSource> {\n  const pathName = normalizeName(name)\n  const obfuscatedPathName = obfuscatePathName(pathName, obfuscate)\n  const path = getNextIntegrationsURL()\n\n  const fullPath = `${path}/integrations/${\n    obfuscatedPathName ?? pathName\n  }/${version}/${obfuscatedPathName ?? pathName}.dynamic.js.gz`\n\n  try {\n    await loadScript(fullPath)\n    recordLoadMetrics(fullPath, ctx, name)\n  } catch (err) {\n    ctx.stats.gauge('legacy_destination_time', -1, [`plugin:${name}`, `failed`])\n    throw err\n  }\n\n  // @ts-ignore\n  const deps: string[] = window[`${pathName}Deps`]\n  await Promise.all(deps.map((dep) => loadScript(path + dep + '.gz')))\n\n  // @ts-ignore\n  window[`${pathName}Loader`]()\n\n  return window[\n    // @ts-ignore\n    `${pathName}Integration`\n  ] as ClassicIntegrationSource\n}\n\nexport async function unloadIntegration(\n  name: string,\n  version: string,\n  obfuscate?: boolean\n): Promise<void> {\n  const path = getNextIntegrationsURL()\n  const pathName = normalizeName(name)\n  const obfuscatedPathName = obfuscatePathName(name, obfuscate)\n\n  const fullPath = `${path}/integrations/${\n    obfuscatedPathName ?? pathName\n  }/${version}/${obfuscatedPathName ?? pathName}.dynamic.js.gz`\n\n  return unloadScript(fullPath)\n}\n\nexport function resolveVersion(\n  settings?: LegacyIntegrationConfiguration\n): string {\n  return (\n    settings?.versionSettings?.override ??\n    settings?.versionSettings?.version ??\n    'latest'\n  )\n}\n", "import { Integrations, JSONObject } from '../../core/events'\nimport { Alias, Facade, Group, Identify, Page, Track } from '@segment/facade'\nimport { Analytics, InitOptions } from '../../core/analytics'\nimport { LegacySettings } from '../../browser'\nimport { isOffline, isOnline } from '../../core/connection'\nimport { Context, ContextCancelation } from '../../core/context'\nimport { isServer } from '../../core/environment'\nimport { DestinationPlugin, Plugin } from '../../core/plugin'\nimport { attempt } from '@segment/analytics-core'\nimport { isPlanEventEnabled } from '../../lib/is-plan-event-enabled'\nimport { mergedOptions } from '../../lib/merged-options'\nimport { pWhile } from '../../lib/p-while'\nimport { PriorityQueue } from '../../lib/priority-queue'\nimport { PersistedPriorityQueue } from '../../lib/priority-queue/persisted'\nimport {\n  applyDestinationMiddleware,\n  DestinationMiddlewareFunction,\n} from '../middleware'\nimport {\n  buildIntegration,\n  loadIntegration,\n  resolveIntegrationNameFromSource,\n  resolveVersion,\n  unloadIntegration,\n} from './loader'\nimport { LegacyIntegration, ClassicIntegrationSource } from './types'\nimport { isPlainObject } from '@segment/analytics-core'\nimport {\n  isDisabledIntegration as shouldSkipIntegration,\n  isInstallableIntegration,\n} from './utils'\n\nexport type ClassType<T> = new (...args: unknown[]) => T\n\nasync function flushQueue(\n  xt: Plugin,\n  queue: PriorityQueue<Context>\n): Promise<PriorityQueue<Context>> {\n  const failedQueue: Context[] = []\n\n  if (isOffline()) {\n    return queue\n  }\n\n  await pWhile(\n    () => queue.length > 0 && isOnline(),\n    async () => {\n      const ctx = queue.pop()\n      if (!ctx) {\n        return\n      }\n\n      const result = await attempt(ctx, xt)\n      const success = result instanceof Context\n      if (!success) {\n        failedQueue.push(ctx)\n      }\n    }\n  )\n\n  // re-add failed tasks\n  failedQueue.map((failed) => queue.pushWithBackoff(failed))\n  return queue\n}\n\nexport class LegacyDestination implements DestinationPlugin {\n  name: string\n  version: string\n  settings: JSONObject\n  options: InitOptions = {}\n  type: Plugin['type'] = 'destination'\n  middleware: DestinationMiddlewareFunction[] = []\n\n  private _ready = false\n  private _initialized = false\n  private onReady: Promise<unknown> | undefined\n  private onInitialize: Promise<unknown> | undefined\n  private disableAutoISOConversion: boolean\n\n  integrationSource?: ClassicIntegrationSource\n  integration: LegacyIntegration | undefined\n\n  buffer: PriorityQueue<Context>\n  flushing = false\n\n  constructor(\n    name: string,\n    version: string,\n    settings: JSONObject = {},\n    options: InitOptions,\n    integrationSource?: ClassicIntegrationSource\n  ) {\n    this.name = name\n    this.version = version\n    this.settings = { ...settings }\n    this.disableAutoISOConversion = options.disableAutoISOConversion || false\n    this.integrationSource = integrationSource\n\n    // AJS-Renderer sets an extraneous `type` setting that clobbers\n    // existing type defaults. We need to remove it if it's present\n    if (this.settings['type'] && this.settings['type'] === 'browser') {\n      delete this.settings['type']\n    }\n\n    this.options = options\n    this.buffer = options.disableClientPersistence\n      ? new PriorityQueue(4, [])\n      : new PersistedPriorityQueue(4, `dest-${name}`)\n\n    this.scheduleFlush()\n  }\n\n  isLoaded(): boolean {\n    return this._ready\n  }\n\n  ready(): Promise<unknown> {\n    return this.onReady ?? Promise.resolve()\n  }\n\n  async load(ctx: Context, analyticsInstance: Analytics): Promise<void> {\n    if (this._ready || this.onReady !== undefined) {\n      return\n    }\n\n    const integrationSource =\n      this.integrationSource ??\n      (await loadIntegration(\n        ctx,\n        this.name,\n        this.version,\n        this.options.obfuscate\n      ))\n\n    this.integration = buildIntegration(\n      integrationSource,\n      this.settings,\n      analyticsInstance\n    )\n\n    this.onReady = new Promise((resolve) => {\n      const onReadyFn = (): void => {\n        this._ready = true\n        resolve(true)\n      }\n\n      this.integration!.once('ready', onReadyFn)\n    })\n\n    this.onInitialize = new Promise((resolve) => {\n      const onInit = (): void => {\n        this._initialized = true\n        resolve(true)\n      }\n\n      this.integration!.on('initialize', onInit)\n    })\n\n    try {\n      ctx.stats.increment('analytics_js.integration.invoke', 1, [\n        `method:initialize`,\n        `integration_name:${this.name}`,\n      ])\n\n      this.integration.initialize()\n    } catch (error) {\n      ctx.stats.increment('analytics_js.integration.invoke.error', 1, [\n        `method:initialize`,\n        `integration_name:${this.name}`,\n      ])\n\n      throw error\n    }\n  }\n\n  unload(_ctx: Context, _analyticsInstance: Analytics): Promise<void> {\n    return unloadIntegration(this.name, this.version, this.options.obfuscate)\n  }\n\n  addMiddleware(...fn: DestinationMiddlewareFunction[]): void {\n    this.middleware = this.middleware.concat(...fn)\n  }\n\n  shouldBuffer(ctx: Context): boolean {\n    return (\n      // page events can't be buffered because of destinations that automatically add page views\n      ctx.event.type !== 'page' &&\n      (isOffline() || this._ready === false || this._initialized === false)\n    )\n  }\n\n  private async send<T extends Facade>(\n    ctx: Context,\n    clz: ClassType<T>,\n    eventType: 'track' | 'identify' | 'page' | 'alias' | 'group'\n  ): Promise<Context> {\n    if (this.shouldBuffer(ctx)) {\n      this.buffer.push(ctx)\n      this.scheduleFlush()\n      return ctx\n    }\n\n    const plan = this.options?.plan?.track\n    const ev = ctx.event.event\n\n    if (plan && ev && this.name !== 'june.so') {\n      // events are always sent to segment (legacy behavior)\n      const planEvent = plan[ev]\n      if (!isPlanEventEnabled(plan, planEvent)) {\n        ctx.updateEvent('integrations', {\n          ...ctx.event.integrations,\n          All: false,\n          'june.so': true,\n        })\n        ctx.cancel(\n          new ContextCancelation({\n            retry: false,\n            reason: `Event ${ev} disabled for integration ${this.name} in tracking plan`,\n            type: 'Dropped by plan',\n          })\n        )\n        return ctx\n      } else {\n        ctx.updateEvent('integrations', {\n          ...ctx.event.integrations,\n          ...planEvent?.integrations,\n        })\n      }\n\n      if (planEvent?.enabled && planEvent?.integrations![this.name] === false) {\n        ctx.cancel(\n          new ContextCancelation({\n            retry: false,\n            reason: `Event ${ev} disabled for integration ${this.name} in tracking plan`,\n            type: 'Dropped by plan',\n          })\n        )\n        return ctx\n      }\n    }\n\n    const afterMiddleware = await applyDestinationMiddleware(\n      this.name,\n      ctx.event,\n      this.middleware\n    )\n\n    if (afterMiddleware === null) {\n      return ctx\n    }\n\n    const event = new clz(afterMiddleware, {\n      traverse: !this.disableAutoISOConversion,\n    })\n\n    ctx.stats.increment('analytics_js.integration.invoke', 1, [\n      `method:${eventType}`,\n      `integration_name:${this.name}`,\n    ])\n\n    try {\n      if (this.integration) {\n        await this.integration.invoke.call(this.integration, eventType, event)\n      }\n    } catch (err) {\n      ctx.stats.increment('analytics_js.integration.invoke.error', 1, [\n        `method:${eventType}`,\n        `integration_name:${this.name}`,\n      ])\n      throw err\n    }\n\n    return ctx\n  }\n\n  async track(ctx: Context): Promise<Context> {\n    return this.send(ctx, Track as ClassType<Track>, 'track')\n  }\n\n  async page(ctx: Context): Promise<Context> {\n    if (this.integration?._assumesPageview && !this._initialized) {\n      this.integration.initialize()\n    }\n\n    return this.onInitialize!.then(() => {\n      return this.send(ctx, Page as ClassType<Page>, 'page')\n    })\n  }\n\n  async identify(ctx: Context): Promise<Context> {\n    return this.send(ctx, Identify as ClassType<Identify>, 'identify')\n  }\n\n  async alias(ctx: Context): Promise<Context> {\n    return this.send(ctx, Alias as ClassType<Alias>, 'alias')\n  }\n\n  async group(ctx: Context): Promise<Context> {\n    return this.send(ctx, Group as ClassType<Group>, 'group')\n  }\n\n  private scheduleFlush(): void {\n    if (this.flushing) {\n      return\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-misused-promises\n    setTimeout(async () => {\n      this.flushing = true\n      this.buffer = await flushQueue(this, this.buffer)\n      this.flushing = false\n\n      if (this.buffer.todo > 0) {\n        this.scheduleFlush()\n      }\n    }, Math.random() * 5000)\n  }\n}\n\nexport function ajsDestinations(\n  settings: LegacySettings,\n  globalIntegrations: Integrations = {},\n  options: InitOptions = {},\n  routingMiddleware?: DestinationMiddlewareFunction,\n  legacyIntegrationSources?: ClassicIntegrationSource[]\n): LegacyDestination[] {\n  if (isServer()) {\n    return []\n  }\n\n  if (settings.plan) {\n    options = options ?? {}\n    options.plan = settings.plan\n  }\n\n  const routingRules = settings.middlewareSettings?.routingRules ?? []\n  const remoteIntegrationsConfig = settings.integrations\n  const localIntegrationsConfig = options.integrations\n  // merged remote CDN settings with user provided options\n  const integrationOptions = mergedOptions(settings, options ?? {}) as Record<\n    string,\n    JSONObject\n  >\n\n  const adhocIntegrationSources = legacyIntegrationSources?.reduce(\n    (acc, integrationSource) => ({\n      ...acc,\n      [resolveIntegrationNameFromSource(integrationSource)]: integrationSource,\n    }),\n    {} as Record<string, ClassicIntegrationSource>\n  )\n\n  const installableIntegrations = new Set([\n    // Remotely configured installable integrations\n    ...Object.keys(remoteIntegrationsConfig).filter((name) =>\n      isInstallableIntegration(name, remoteIntegrationsConfig[name])\n    ),\n\n    // Directly provided integration sources are only installable if settings for them are available\n    ...Object.keys(adhocIntegrationSources || {}).filter(\n      (name) =>\n        isPlainObject(remoteIntegrationsConfig[name]) ||\n        isPlainObject(localIntegrationsConfig?.[name])\n    ),\n  ])\n\n  return Array.from(installableIntegrations)\n    .filter((name) => !shouldSkipIntegration(name, globalIntegrations))\n    .map((name) => {\n      const integrationSettings = remoteIntegrationsConfig[name]\n      const version = resolveVersion(integrationSettings)\n      const destination = new LegacyDestination(\n        name,\n        version,\n        integrationOptions[name],\n        options,\n        adhocIntegrationSources?.[name]\n      )\n\n      const routing = routingRules.filter(\n        (rule) => rule.destinationName === name\n      )\n      if (routing.length > 0 && routingMiddleware) {\n        destination.addMiddleware(routingMiddleware)\n      }\n\n      return destination\n    })\n}\n", "import { Integrations } from '@segment/analytics-core'\nimport { LegacyIntegrationConfiguration } from '../..'\n\nexport const isInstallableIntegration = (\n  name: string,\n  integrationSettings: LegacyIntegrationConfiguration\n) => {\n  const { type, bundlingStatus, versionSettings } = integrationSettings\n  // We use `!== 'unbundled'` (versus `=== 'bundled'`) to be inclusive of\n  // destinations without a defined value for `bundlingStatus`\n  const deviceMode =\n    bundlingStatus !== 'unbundled' &&\n    (type === 'browser' || versionSettings?.componentTypes?.includes('browser'))\n\n  // checking for iterable is a quick fix we need in place to prevent\n  // errors showing Iterable as a failed destiantion. Ideally, we should\n  // fix the Iterable metadata instead, but that's a longer process.\n  return !name.startsWith('Segment') && name !== 'Iterable' && deviceMode\n}\n\nexport const isDisabledIntegration = (\n  integrationName: string,\n  globalIntegrations: Integrations\n) => {\n  const allDisableAndNotDefined =\n    globalIntegrations.All === false &&\n    globalIntegrations[integrationName] === undefined\n\n  return (\n    globalIntegrations[integrationName] === false || allDisableAndNotDefined\n  )\n}\n"], "names": ["isPlainObject", "obj", "Object", "prototype", "toString", "call", "slice", "toLowerCase", "isPlanEventEnabled", "plan", "planEvent", "enabled", "__default", "normalizeName", "name", "replace", "obfuscatePathName", "pathName", "obfuscate", "btoa", "undefined", "loadIntegration", "ctx", "version", "Promise", "obfuscatedPathName", "path", "fullPath", "metric", "window", "performance", "getEntriesByName", "stats", "gauge", "Math", "round", "duration", "_", "recordLoadMetrics", "deps", "all", "map", "dep", "flushQueue", "xt", "queue", "failedQueue", "length", "pop", "a", "result", "push", "failed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings", "options", "integrationSource", "type", "middleware", "_ready", "_initialized", "flushing", "this", "disableAutoISOConversion", "buffer", "disableClientPersistence", "scheduleFlush", "isLoaded", "ready", "onReady", "resolve", "load", "analyticsInstance", "integration", "integrationSettings", "integrationCtr", "user", "addIntegration", "Integration", "analytics", "buildIntegration", "once", "onInitialize", "on", "increment", "initialize", "error", "unload", "_ctx", "_analyticsInstance", "unloadIntegration", "addMiddleware", "concat", "fn", "<PERSON><PERSON><PERSON><PERSON>", "event", "send", "clz", "eventType", "track", "ev", "updateEvent", "integrations", "All", "cancel", "retry", "reason", "applyDestinationMiddleware", "afterMiddleware", "traverse", "invoke", "Track", "page", "_assumesPageview", "then", "Page", "identify", "Identify", "alias", "<PERSON><PERSON>", "group", "Group", "setTimeout", "todo", "random", "ajsDestinations", "globalIntegrations", "routingMiddleware", "legacyIntegrationSources", "routingRules", "middlewareSettings", "remoteIntegrationsConfig", "localIntegrationsConfig", "integrationOptions", "adhocIntegrationSources", "reduce", "acc", "resolveIntegrationNameFromSource", "installableIntegrations", "Set", "keys", "filter", "bundlingStatus", "versionSettings", "deviceMode", "componentTypes", "includes", "startsWith", "isInstallableIntegration", "Array", "from", "integrationName", "allDisableAndNotDefined", "override", "resolveVersion", "destination", "LegacyDestination", "rule", "destinationName"], "sourceRoot": ""}