{"version": 3, "file": "eslint.js", "sourceRoot": "", "sources": ["../../src/util/eslint.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,MAAM,EAAE,MAAM,YAAY,CAAC;AAEpC,MAAM,UAAU,GACd,iKAAiK,CAAC;AAEpK,MAAM,eAAe,GAAG,cAAc,CAAC;AAWvC;;;;;;;;;;;;GAYG;AACH,MAAM,UAAU,mBAAmB,CAAC,OAAgB;IAClD,4FAA4F;IAC5F,2DAA2D;IAC3D,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,GAAG;QAAE,OAAO,SAAS,CAAC;IAErD,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC9C,IAAI,CAAC,MAAM;QAAE,OAAO,SAAS,CAAC;IAC9B,IAAI,CAAC,MAAM,CAAC,MAAM;QAAE,OAAO,SAAS,CAAC;IAErC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC;IACxD,IAAI,MAAM,KAAK,SAAS,IAAI,QAAQ,KAAK,SAAS;QAAE,OAAO,SAAS,CAAC;IACrE,MAAM,OAAO,GAAG,QAAQ;SACrB,KAAK,CAAC,GAAG,CAAC;SACV,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACrB,wBAAwB;SACvB,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,KAAK,EAAE,CAAC,CAAC;IAErC,MAAM,KAAK,GAAG,MAAM,KAAK,0BAA0B,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC;IAC3E,4CAA4C;IAC5C,IAAI,KAAK,KAAK,MAAM,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM;QAAE,OAAO,SAAS,CAAC;IAElE,OAAO;QACL,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,KAAK,EAAE,MAAM,KAAK,0BAA0B,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM;QACnE,OAAO;QACP,0BAA0B;QAC1B,GAAG,CAAC,WAAW,KAAK,EAAE,IAAI,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC;QAC3E,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,GAAG,EAAE,OAAO,CAAC,GAAG;KACjB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,IAA8C;IAC1E,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;IAC5B,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;QACpB,OAAO,MAAM,IAAI,EAAE,CAAC;IACtB,CAAC;SAAM,CAAC;QACN,OAAO,MAAM,IAAI,KAAK,CAAC;IACzB,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,oBAAoB,CAAC,EACnC,IAAI,EACJ,KAAK,EACL,OAAO,EACP,WAAW,GAC2B;IACtC,MAAM,MAAM,GAAG,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAC,CAAC,gBAAgB,CAAC;IACrF,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5C,MAAM,MAAM,GAAG,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,WAAW,EAAE,CAAC;IACrE,OAAO,aAAa,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,QAAQ,GAAG,MAAM,EAAE,EAAE,CAAC,CAAC;AACzE,CAAC;AAED,SAAS,iBAAiB,CAAC,UAAsB,EAAE,IAAY;IAC7D,MAAM,aAAa,GAAG,UAAU,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IACtE,qDAAqD;IACrD,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAClC,aAAa,EACb,aAAa;QACX,UAAU,CAAC,IAAI;aACZ,KAAK,CAAC,aAAa,CAAC;YACrB,gDAAgD;YAChD,4CAA4C;aAC3C,MAAM,CAAC,4DAA4D,CAAC,CAC1E,CAAC;IACF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,eAAe,CAAC,UAAsB,EAAE,IAAY;IAC3D,MAAM,aAAa,GAAG,UAAU,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IACtE,MAAM,QAAQ,GAAG,UAAU,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;IAC/D,OAAO,QAAQ,EAAE,IAAI,KAAK,SAAS,CAAC;AACtC,CAAC;AAED,MAAM,UAAU,uBAAuB,CAAC,UAAsB,EAAE,IAAY;IAC1E,MAAM,aAAa,GAAG,UAAU,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IACtE,MAAM,QAAQ,GAAG,UAAU,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;IAC/D,OAAO,QAAQ,EAAE,IAAI,KAAK,iBAAiB,CAAC;AAC9C,CAAC;AAED,MAAM,UAAU,kCAAkC,CAAC,UAAsB,EAAE,OAA2B;IACpG,KAAK,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QACzC,MAAM,KAAK,GAAG,UAAU,CAAC,eAAe,CAAC;YACvC,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,iCAAiC;YACjC,MAAM,EAAE,CAAC,GAAG,CAAC;SACd,CAAC,CAAC;QACH,MAAM,IAAI,GAAG,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QACnD,IAAI,IAAI,EAAE,IAAI,KAAK,iBAAiB,EAAE,CAAC;YACrC,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IACD,MAAM,IAAI,KAAK,CAAC,yBAAyB,OAAO,CAAC,IAAI,oCAAoC,CAAC,CAAC;AAC7F,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,YAAY,CAAC,CAAW,EAAE,CAAW;IACnD,OAAO,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9B,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,gBAAgB,CAAC,CAAqB,EAAE,CAAqB;IAC3E,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS;QAAE,OAAO,SAAS,CAAC;IACzD,IAAI,CAAC,KAAK,SAAS;QAAE,OAAO,CAAC,CAAC;IAC9B,IAAI,CAAC,KAAK,SAAS;QAAE,OAAO,CAAC,CAAC;IAC9B,OAAO,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;AACtB,CAAC;AAED,MAAM,UAAU,2CAA2C,CAAC,IAM3D;IACC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;IAC9D,MAAM,MAAM,GAAG,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACnD,MAAM,aAAa,GAAG,UAAU,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;IAEnE,IAAI,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC;QACtC,MAAM,WAAW,GAAG,aAAa,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;QACxE,OAAO,KAAK,CAAC,qBAAqB,CAAC,CAAC,aAAa,EAAE,aAAa,CAAC,EAAE,GAAG,MAAM,IAAI,WAAW,KAAK,CAAC,CAAC;IACpG,CAAC;SAAM,CAAC;QACN,MAAM,WAAW,GAAG,aAAa,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;QACvE,OAAO,KAAK,CAAC,qBAAqB,CAAC,CAAC,aAAa,EAAE,aAAa,CAAC,EAAE,GAAG,MAAM,GAAG,WAAW,IAAI,CAAC,CAAC;IAClG,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,oBAAoB,CAAC,IAKpC;IACC,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,sBAAsB,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;IACzF,MAAM,qBAAqB,GAAG,oBAAoB,CAAC;QACjD,IAAI,EAAE,sBAAsB,CAAC,IAAI;QACjC,KAAK,EAAE,sBAAsB,CAAC,KAAK;QACnC,OAAO,EAAE,QAAQ;QACjB,WAAW,EAAE,cAAc;KAC5B,CAAC,CAAC;IACH,OAAO,KAAK,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAC;AACrF,CAAC;AAED,MAAM,UAAU,uCAAuC,CAAC,IAQvD;IACC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;IAC9E,MAAM,MAAM,GAAG,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACnD,MAAM,aAAa,GAAG,UAAU,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;IACnE,MAAM,WAAW,GAAG,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACtD,MAAM,IAAI,GAAG,WAAW,IAAI,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;IAChE,MAAM,kBAAkB,GAAG,oBAAoB,CAAC;QAC9C,IAAI;QACJ,KAAK;QACL,OAAO;QACP,WAAW;KACZ,CAAC,CAAC;IACH,IAAI,WAAW,EAAE,CAAC;QAChB,OAAO,KAAK,CAAC,qBAAqB,CAAC,CAAC,aAAa,EAAE,aAAa,CAAC,EAAE,GAAG,MAAM,IAAI,kBAAkB,KAAK,CAAC,CAAC;IAC3G,CAAC;SAAM,CAAC;QACN,OAAO,KAAK,CAAC,qBAAqB,CAAC,CAAC,aAAa,EAAE,aAAa,CAAC,EAAE,GAAG,MAAM,GAAG,kBAAkB,IAAI,CAAC,CAAC;IACzG,CAAC;AACH,CAAC;AAOD;;GAEG;AACH,MAAM,UAAU,yBAAyB,CAAC,EAAE,WAAW,EAAE,WAAW,EAAsC;IACxG,MAAM,MAAM,GAAG,QAAQ,CAAC;IACxB,MAAM,eAAe,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;SAChD,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,EAAE;QAC3B,sFAAsF;QACtF,OAAO,GAAG,MAAM,KAAK,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC;IACnD,CAAC,CAAC;SACD,IAAI,CAAC,IAAI,CAAC,CAAC;IACd,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;QAC9B,OAAO,MAAM,MAAM,IAAI,eAAe,KAAK,CAAC;IAC9C,CAAC;SAAM,CAAC;QACN,OAAO,MAAM,MAAM,IAAI,eAAe,OAAO,WAAW,KAAK,CAAC;IAChE,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,qBAAqB,CAAC,OAA4B,EAAE,OAA0B;IAC5F,OAAO,OAAO;SACX,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;QACd,OAAO;YACL,GAAG,MAAM;YACT,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SAChF,CAAC;IACJ,CAAC,CAAC;SACD,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACpD,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,WAAW,CAAC,cAAsB;IAChD,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACpD,IAAI,CAAC,MAAM;QAAE,OAAO,IAAI,CAAC;IACzB,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC;AAC1C,CAAC"}