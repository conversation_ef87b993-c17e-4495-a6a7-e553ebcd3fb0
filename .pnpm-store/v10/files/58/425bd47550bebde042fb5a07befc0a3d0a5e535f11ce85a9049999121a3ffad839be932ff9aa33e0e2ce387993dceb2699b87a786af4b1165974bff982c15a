{"version": 3, "file": "address.js", "sourceRoot": "", "sources": ["../lib/address.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;AAEb,sDAA2B;AAE3B,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;IACjB,OAAO;QAEL,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAE3B,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAErD,OAAO,CACL,kBAAG,CAAC,MAAM,EAAE,UAAU,GAAG,CAAC,CAAC;YAC3B,kBAAG,CAAC,MAAM,EAAE,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAG,CAAC,MAAM,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACxC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC3B,kBAAG,CAAC,KAAK,EAAE,UAAU,GAAG,CAAC,CAAC;YAC1B,kBAAG,CAAC,KAAK,EAAE,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAG,CAAC,KAAK,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACvC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAC3B,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAQD,mBAAyB,KAAK;IAC5B,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;IACvC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;IACjC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC/B,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;IAC7B,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;IAC3B,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;AACjC,CAAC;AAPD,4BAOC", "sourcesContent": ["\"use strict\";\n\nimport get from \"obj-case\";\n\nfunction trait(a, b) {\n  return function () {\n    // @ts-ignore\n    let traits = this.traits();\n    // @ts-ignore\n    let props = this.properties ? this.properties() : {};\n\n    return (\n      get(traits, \"address.\" + a) ||\n      get(traits, a) ||\n      (b ? get(traits, \"address.\" + b) : null) ||\n      (b ? get(traits, b) : null) ||\n      get(props, \"address.\" + a) ||\n      get(props, a) ||\n      (b ? get(props, \"address.\" + b) : null) ||\n      (b ? get(props, b) : null)\n    );\n  };\n}\n\n/**\n * Add address getters to `proto`.\n *\n * @ignore\n * @param {Function} proto\n */\nexport default function (proto) {\n  proto.zip = trait(\"postalCode\", \"zip\");\n  proto.country = trait(\"country\");\n  proto.street = trait(\"street\");\n  proto.state = trait(\"state\");\n  proto.city = trait(\"city\");\n  proto.region = trait(\"region\");\n}\n"]}