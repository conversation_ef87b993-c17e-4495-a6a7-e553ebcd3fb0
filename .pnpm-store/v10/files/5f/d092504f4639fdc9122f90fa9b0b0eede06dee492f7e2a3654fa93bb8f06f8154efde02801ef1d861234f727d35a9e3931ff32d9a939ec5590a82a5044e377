{"name": "@stdlib/number-float64-base-normalize", "version": "0.0.9", "description": "Return a normal number `y` and exponent `exp` satisfying `x = y * 2^exp`.", "license": "Apache-2.0", "author": {"name": "The Stdlib Authors", "url": "https://github.com/stdlib-js/stdlib/graphs/contributors"}, "contributors": [{"name": "The Stdlib Authors", "url": "https://github.com/stdlib-js/stdlib/graphs/contributors"}], "main": "./lib", "directories": {"benchmark": "./benchmark", "doc": "./docs", "example": "./examples", "include": "./include", "lib": "./lib", "src": "./src", "test": "./test"}, "types": "./docs/types", "scripts": {"test": "make test", "test-cov": "make test-cov", "examples": "make examples", "benchmark": "make benchmark"}, "homepage": "https://stdlib.io", "repository": {"type": "git", "url": "git://github.com/stdlib-js/number-float64-base-normalize.git"}, "bugs": {"url": "https://github.com/stdlib-js/stdlib/issues"}, "dependencies": {"@stdlib/constants-float64-smallest-normal": "^0.0.x", "@stdlib/math-base-assert-is-infinite": "^0.0.x", "@stdlib/math-base-assert-is-nan": "^0.0.x", "@stdlib/math-base-special-abs": "^0.0.x", "@stdlib/types": "^0.0.x", "@stdlib/utils-define-nonenumerable-read-only-property": "^0.0.x", "@stdlib/utils-library-manifest": "^0.0.x"}, "devDependencies": {"@stdlib/array-float64": "^0.0.x", "@stdlib/assert-has-own-property": "^0.0.x", "@stdlib/assert-is-array": "^0.0.x", "@stdlib/assert-is-float64array": "^0.0.x", "@stdlib/bench": "^0.0.x", "@stdlib/constants-float64-ninf": "^0.0.x", "@stdlib/constants-float64-pinf": "^0.0.x", "@stdlib/constants-float64-smallest-subnormal": "^0.0.x", "@stdlib/math-base-special-pow": "^0.0.x", "@stdlib/random-base-discrete-uniform": "^0.0.x", "@stdlib/random-base-randu": "^0.0.x", "@stdlib/random-base-uniform": "^0.0.x", "@stdlib/utils-try-require": "^0.0.x", "tape": "git+https://github.com/kgryte/tape.git#fix/globby", "istanbul": "^0.4.1", "tap-spec": "5.x.x"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "keywords": ["stdlib", "stdtypes", "base", "utilities", "utility", "utils", "util", "types", "type", "float64", "double", "dbl", "floating-point", "ieee754", "denormalized", "normalize", "subnormal", "number", "normal", "float"], "funding": {"type": "patreon", "url": "https://www.patreon.com/athan"}}