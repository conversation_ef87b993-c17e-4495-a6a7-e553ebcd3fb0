{"version": 3, "file": "interfaces.d.ts", "sourceRoot": "", "sources": ["../../../../src/core/analytics/interfaces.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,SAAS,EAAE,iBAAiB,EAAE,WAAW,EAAE,MAAM,GAAG,CAAA;AAClE,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,WAAW,CAAA;AACvC,OAAO,KAAK,EACV,WAAW,EACX,eAAe,EACf,UAAU,EACV,cAAc,EACd,WAAW,EACX,WAAW,EACZ,MAAM,uBAAuB,CAAA;AAC9B,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,YAAY,CAAA;AACzC,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,WAAW,CAAA;AAC7C,OAAO,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,SAAS,CAAA;AAC1C,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,qCAAqC,CAAA;AAC5E,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAA;AAMvD;;GAEG;AACH,kBAAkB;AAClB,UAAU,qBAAqB;IAC7B,kBAAkB;IAClB,GAAG,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,CAAA;IACtB,kBAAkB;IAClB,wBAAwB,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,CAAA;IAC3C,kBAAkB;IAClB,SAAS,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,CAAA;IAC5B,kBAAkB;IAClB,gBAAgB,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,CAAA;IACnC,kBAAkB;IAClB,kBAAkB,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,CAAA;IACrC,kBAAkB;IAClB,cAAc,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,CAAA;IACjC,kBAAkB;IAClB,mBAAmB,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,CAAA;IACtC,kBAAkB;IAClB,YAAY,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,CAAA;IAC/B,kBAAkB;IAElB,cAAc,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,CAAA;IACjC,kBAAkB;IAClB,GAAG,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,CAAA;CACvB;AAED,kBAAkB;AAClB,MAAM,WAAW,gBAAiB,SAAQ,qBAAqB;IAC7D,kBAAkB;IAClB,UAAU,CACR,QAAQ,CAAC,EAAE,iBAAiB,EAC5B,OAAO,CAAC,EAAE,WAAW,GACpB,OAAO,CAAC,SAAS,CAAC,CAAA;IAErB,kBAAkB;IAClB,UAAU,IAAI,SAAS,CAAA;IAEvB,kBAAkB;IAClB,SAAS,CAAC,GAAG,EAAE,YAAY,GAAG,YAAY,CAAA;IAE1C,kBAAkB;IAClB,QAAQ,CAAC,qBAAqB,EAAE,MAAM,EAAE,CAAA;IAExC,kBAAkB;IAClB,QAAQ,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,CAAA;IAEzC,kBAAkB;IAClB,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAA;IAErB,kBAAkB;IAClB,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAA;CACzD;AAED;;GAEG;AACH,MAAM,WAAW,aAAc,SAAQ,aAAa;IAClD,KAAK,CAAC,GAAG,IAAI,EAAE,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC,CAAA;IACrD,IAAI,CAAC,GAAG,IAAI,EAAE,UAAU,GAAG,OAAO,CAAC,eAAe,CAAC,CAAA;IACnD,QAAQ,CAAC,GAAG,IAAI,EAAE,cAAc,GAAG,OAAO,CAAC,eAAe,CAAC,CAAA;IAC3D,KAAK,IAAI,KAAK,CAAA;IACd,KAAK,CAAC,GAAG,IAAI,EAAE,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC,CAAA;IACrD,KAAK,CAAC,GAAG,IAAI,EAAE,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC,CAAA;IACrD,MAAM,CAAC,GAAG,IAAI,EAAE,UAAU,GAAG,OAAO,CAAC,eAAe,CAAC,CAAA;IACrD,QAAQ,CAAC,GAAG,OAAO,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,CAAA;IAChD,UAAU,CAAC,GAAG,OAAO,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,CAAA;IAClD,IAAI,IAAI,IAAI,CAAA;IACZ,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;CACzB;AAED;;GAEG;AACH,MAAM,MAAM,oBAAoB,GAAG,IAAI,CAAC,aAAa,EAAE,OAAO,GAAG,MAAM,CAAC,GAAG;IACzE,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,CAAA;IACvB,KAAK,CAAC,GAAG,IAAI,EAAE,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC,CAAA;IACrD,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,CAAA;CACtB,CAAA"}