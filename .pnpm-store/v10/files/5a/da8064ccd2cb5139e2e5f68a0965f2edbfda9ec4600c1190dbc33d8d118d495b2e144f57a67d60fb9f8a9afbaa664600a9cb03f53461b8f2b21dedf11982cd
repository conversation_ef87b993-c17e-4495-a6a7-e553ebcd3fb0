// @TODO wait for virtual modules HMR to work in vite to use `virtual:` and `\0` prefix
// https://github.com/vitejs/vite/pull/10313
export const STORIES_ID = 'virtual:$histoire-stories'
export const RESOLVED_STORIES_ID = `/__resolved__${STORIES_ID}`
// export const RESOLVED_STORIES_ID = `\0${STORIES_ID}`
export const SETUP_ID = 'virtual:$histoire-setup'
export const NOOP_ID = 'virtual:$histoire-noop'
export const CONFIG_ID = 'virtual:$histoire-config'
export const RESOLVED_CONFIG_ID = `/__resolved__${CONFIG_ID}`
// export const RESOLVED_CONFIG_ID = `\0${CONFIG_ID}`
export const THEME_ID = 'virtual:$histoire-theme'
export const RESOLVED_THEME_ID = `/__resolved__${THEME_ID}.css`
// export const RESOLVED_THEME_ID = `\0${THEME_ID}.css`
export const SEARCH_TITLE_DATA_ID = 'virtual:$histoire-search-title-data'
export const RESOLVED_SEARCH_TITLE_DATA_ID = `/__resolved__${SEARCH_TITLE_DATA_ID}`
// export const RESOLVED_SEARCH_TITLE_DATA_ID = `\0${SEARCH_TITLE_DATA_ID}`
export const SEARCH_DOCS_DATA_ID = 'virtual:$histoire-search-docs-data'
export const RESOLVED_SEARCH_DOCS_DATA_ID = `/__resolved__${SEARCH_DOCS_DATA_ID}`
// export const RESOLVED_SEARCH_DOCS_DATA_ID = `\0${SEARCH_DOCS_DATA_ID}`
export const GENERATED_GLOBAL_SETUP = 'virtual:$histoire-generated-global-setup'
export const RESOLVED_GENERATED_GLOBAL_SETUP = `/__resolved__${GENERATED_GLOBAL_SETUP}`
// export const RESOLVED_GENERATED_GLOBAL_SETUP = `\0${GENERATED_GLOBAL_SETUP}`
export const GENERATED_SETUP_CODE = 'virtual:$histoire-generated-setup-code'
export const RESOLVED_GENERATED_SETUP_CODE = `/__resolved__${GENERATED_SETUP_CODE}`
// export const RESOLVED_GENERATED_SETUP_CODE = `\0${GENERATED_SETUP_CODE}`
export const SUPPORT_PLUGINS_CLIENT = 'virtual:$histoire-support-plugins-client'
export const RESOLVED_SUPPORT_PLUGINS_CLIENT = `/__resolved__${SUPPORT_PLUGINS_CLIENT}`
// export const RESOLVED_SUPPORT_PLUGINS_CLIENT = `\0${SUPPORT_PLUGINS_CLIENT}`
export const SUPPORT_PLUGINS_COLLECT = 'virtual:$histoire-support-plugins-collect'
export const RESOLVED_SUPPORT_PLUGINS_COLLECT = `/__resolved__${SUPPORT_PLUGINS_COLLECT}`
// export const RESOLVED_SUPPORT_PLUGINS_COLLECT = `\0${SUPPORT_PLUGINS_COLLECT}`
export const MARKDOWN_FILES = 'virtual:$histoire-markdown-files'
export const RESOLVED_MARKDOWN_FILES = `/__resolved__${MARKDOWN_FILES}`
// export const RESOLVED_MARKDOWN_FILES = `\0${MARKDOWN_FILES}`
export const COMMANDS = 'virtual:$histoire-commands'
export const RESOLVED_COMMANDS = `/__resolved__${COMMANDS}`

export * from './markdown.js'
export * from './noop.js'
export * from './resolved-commands.js'
export * from './resolved-config.js'
export * from './resolved-generated-global-setup.js'
export * from './resolved-generated-setup-code.js'
export * from './resolved-markdown-files.js'
export * from './resolved-stories.js'
export * from './resolved-support-plugins-client.js'
export * from './resolved-support-plugins-collect.js'
export * from './resolved-theme.js'
export * from './story.js'
export * from './story-source.js'
