import type { Literal, RegExpLiteral } from "estree";
import type { <PERSON><PERSON><PERSON><PERSON>al, JSONRegExpLiteral } from "./ast";
export declare function isRegExpLiteral(node: JSONLiteral): node is JSONRegExpLiteral;
export declare function isRegExpLiteral(node: Literal): node is RegExpLiteral;
export declare function isRegExpLiteral(node: JSONLiteral | Literal): node is JSONRegExpLiteral | RegExpLiteral;
