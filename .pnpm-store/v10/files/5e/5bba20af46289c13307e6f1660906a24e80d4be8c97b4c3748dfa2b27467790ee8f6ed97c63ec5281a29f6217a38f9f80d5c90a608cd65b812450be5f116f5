{"version": 3, "file": "linter.js", "sourceRoot": "", "sources": ["../../src/eslint/linter.ts"], "names": [], "mappings": "AAAA,mHAAmH;AACnH,+FAA+F;AAU/F,OAAO,EAAE,iBAAiB,EAAE,MAAM,cAAc,CAAC;AACjD,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AAEzD,MAAM,kBAAkB,GAAG,EAAE,CAAC;AAO9B;;;GAGG;AACH,sCAAsC;AACtC,MAAM,CAAC,KAAK,UAAU,YAAY,CAChC,MAAc,EACd,IAAY,EACZ,QAAgB,EAChB,OAAiB,EACjB,UAA+C;IAE/C,IAAI,WAAwB,CAAC;IAC7B,IAAI,KAAK,GAAG,KAAK,CAAC;IAClB,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,WAAW,GAAG,IAAI,CAAC;IAEvB;;;;;;;;OAQG;IACH,GAAG,CAAC;QACF,UAAU,EAAE,CAAC;QAEb,4CAA4C;QAC5C,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;QACjE,MAAM,QAAQ,GAAG,OAAO;aACrB,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;aACpC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;QAC3E,MAAM,UAAU,GAAG,iBAAiB,EAAE,CAAC;QACvC,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QAExE,sBAAsB;QACtB,MAAM,UAAU,GAAe;YAC7B,QAAQ,EAAE,QAAQ;YAClB,UAAU;YACV,QAAQ;YACR,OAAO;YACP,KAAK,EAAE,SAAS;SACjB,CAAC;QACF,MAAM,KAAK,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC;QAErC,WAAW,GAAG,eAAe,CAAC,UAAU,CACtC,WAAW,EACX,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;YAChB,OAAO;gBACL,MAAM,EAAE,wBAAwB;gBAChC,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,CAAC;gBACT,GAAG;aACJ,CAAC;QACJ,CAAC,CAAC,EACF,IAAI,CACL,CAAC;QAEF;;;WAGG;QACH,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YAC9D,MAAM;QACR,CAAC;QAED,yEAAyE;QACzE,KAAK,GAAG,KAAK,IAAI,WAAW,CAAC,KAAK,CAAC;QAEnC,8DAA8D;QAC9D,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC;IACnC,CAAC,QAAQ,WAAW,CAAC,KAAK,IAAI,UAAU,GAAG,kBAAkB,EAAE;IAE/D,8DAA8D;IAC9D,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;IAC1B,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC;IAEjC,OAAO,WAAW,CAAC;AACrB,CAAC"}