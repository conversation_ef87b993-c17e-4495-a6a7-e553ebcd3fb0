import DatePicker from 'vue-datepicker-next';

var locale = {
  months: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>ha<PERSON><PERSON><PERSON>'],
  monthsShort: ['<PERSON>', 'Ch<PERSON>', 'Maw', '<PERSON><PERSON>r', '<PERSON>', '<PERSON>h', 'Gor', 'Aws', 'Med', 'Hyd', 'Tach', 'Rhag'],
  weekdays: ['Dydd Sul', '<PERSON>ydd <PERSON>lun', '<PERSON><PERSON><PERSON> Mawrth', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>ydd Sadwrn'],
  weekdaysShort: ['Sul', 'Llun', 'Maw', 'Mer', 'Iau', 'Gwe', 'Sad'],
  weekdaysMin: ['Su', 'Ll', 'Ma', 'Me', 'Ia', 'Gw', 'Sa'],
  firstDayOfWeek: 0,
  firstWeekContainsDate: 1
};

const lang = {
    formatLocale: locale,
    yearFormat: 'YYYY',
    monthFormat: 'MMM',
    monthBeforeYear: true,
};
DatePicker.locale('cy', lang);

export { lang as default };
