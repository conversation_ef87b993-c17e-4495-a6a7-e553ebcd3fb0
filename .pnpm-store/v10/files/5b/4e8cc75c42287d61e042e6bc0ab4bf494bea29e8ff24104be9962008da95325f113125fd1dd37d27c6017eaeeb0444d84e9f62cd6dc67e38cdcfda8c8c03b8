{"name": "postcss-custom-selectors", "description": "Use Custom Selectors in CSS", "version": "7.1.3", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>"}, {"name": "yisi"}], "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "engines": {"node": "^14 || ^16 || >=18"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "dependencies": {"@csstools/cascade-layer-name-parser": "^1.0.2", "@csstools/css-parser-algorithms": "^2.1.1", "@csstools/css-tokenizer": "^2.1.1", "postcss-selector-parser": "^6.0.4"}, "peerDependencies": {"postcss": "^8.4"}, "devDependencies": {"@csstools/postcss-tape": "*"}, "scripts": {"build": "rollup -c ../../rollup/default.mjs", "docs": "node ../../.github/bin/generate-docs/install.mjs && node ../../.github/bin/generate-docs/readme.mjs", "lint": "node ../../.github/bin/format-package-json.mjs", "prepublishOnly": "npm run build && npm run test", "test": "node .tape.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "test:rewrite-expects": "REWRITE_EXPECTS=true node .tape.mjs"}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-custom-selectors#readme", "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-custom-selectors"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["at-rule", "at<PERSON>le", "css", "csswg", "custom", "declarative", "extensions", "postcss", "postcss-plugin", "rule", "selectors", "specification", "w3c"], "csstools": {"assumesToProcessBundledCSS": true, "cssdbId": "custom-selectors", "exportName": "postcssCustomSelectors", "humanReadableName": "PostCSS Custom Selectors", "specUrl": "https://drafts.csswg.org/css-extensions/#custom-selectors"}, "volta": {"extends": "../../package.json"}}