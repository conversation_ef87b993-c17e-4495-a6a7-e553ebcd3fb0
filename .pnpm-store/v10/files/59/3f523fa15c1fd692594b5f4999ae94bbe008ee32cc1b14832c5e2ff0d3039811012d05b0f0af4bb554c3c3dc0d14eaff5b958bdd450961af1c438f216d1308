var e=[" ",">","~",":","+","@","#","(",")"];function isValidReplacement(t){for(var n=!0,i=0,c=e.length;i<c&&n;i++)t.indexOf(e[i])>-1&&(n=!1);return n}function focusWithin(e){var t={force:!1,replaceWith:"[focus-within]"};if(void 0!==e&&"force"in e&&(t.force=e.force),void 0!==e&&"replaceWith"in e&&(t.replaceWith=e.replaceWith),!isValidReplacement(t.replaceWith))throw new Error(t.replaceWith+" is not a valid replacement since it can't be applied to single elements.");try{if(document.querySelector(":focus-within"),!t.force)return}catch(d){}var n,i,c,o,r,a=(n=t.replaceWith,r=[],"."===n[0]?(i=n.slice(1),c=function remove(e){return e.classList.remove(i)},o=function add(e){return e.classList.add(i)}):(i=n.slice(1,-1),c=function remove(e){return e.removeAttribute(i,"")},o=function add(e){return e.setAttribute(i,"")}),function handleFocusChange(){r.forEach((function(e){return c(e)})),r.length=0;var e=document.activeElement;if(!/^(#document|HTML|BODY)$/.test(Object(e).nodeName))for(;e&&1===e.nodeType;)o(e),r.push(e),e=e.parentNode}),u=function initializeEventListeners(){document.documentElement.className.indexOf("js-focus-within")>-1||(document.documentElement.className=document.documentElement.className+" js-focus-within",document.addEventListener("focus",a,!0),document.addEventListener("blur",a,!0))};"complete"===document.readyState?u():document.addEventListener("DOMContentLoaded",u)}export{focusWithin as default};
//# sourceMappingURL=browser.mjs.map
