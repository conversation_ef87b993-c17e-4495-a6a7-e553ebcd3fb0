@charset "UTF-8";
body {
  font: 20px/1.5 "Helvetica Neue", Helvetica, Arial, sans-serif;
  padding: 60px 50px;
}

#mocha ul, #mocha li {
  margin: 0;
  padding: 0;
}

#mocha ul {
  list-style: none;
}

#mocha h1, #mocha h2 {
  margin: 0;
}

#mocha h1 {
  margin-top: 15px;
  font-size: 1em;
  font-weight: 200;
}

#mocha h1 a {
  text-decoration: none;
  color: inherit;
}

#mocha h1 a:hover {
  text-decoration: underline;
}

#mocha .suite .suite h1 {
  margin-top: 0;
  font-size: .8em;
}

.hidden {
  display: none;
}

#mocha h2 {
  font-size: 12px;
  font-weight: normal;
  cursor: pointer;
}

#mocha .suite {
  margin-left: 15px;
}

#mocha .test {
  margin-left: 15px;
}

#mocha .test:hover h2::after {
  position: relative;
  top: 0;
  right: -10px;
  content: '(view source)';
  font-size: 12px;
  font-family: arial;
  color: #888;
}

#mocha .test.pending:hover h2::after {
  content: '(pending)';
  font-family: arial;
}

#mocha .test.pass.medium .duration {
  background: #C09853;
}

#mocha .test.pass.slow .duration {
  background: #B94A48;
}

#mocha .test.pass::before {
  content: '✓';
  font-size: 12px;
  display: block;
  float: left;
  margin-right: 5px;
  color: #00d6b2;
}

#mocha .test.pass .duration {
  font-size: 9px;
  margin-left: 5px;
  padding: 2px 5px;
  color: white;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.2);
  -moz-box-shadow: inset 0 1px 1px rgba(0,0,0,.2);
  box-shadow: inset 0 1px 1px rgba(0,0,0,.2);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  border-radius: 5px;
}

#mocha .test.pass.fast .duration {
  display: none;
}

#mocha .test.pending {
  color: #0b97c4;
}

#mocha .test.pending::before {
  content: '◦';
  color: #0b97c4;
}

#mocha .test.fail {
  color: #c00;
}

#mocha .test.fail pre {
  color: black;
}

#mocha .test.fail::before {
  content: '✖';
  font-size: 12px;
  display: block;
  float: left;
  margin-right: 5px;
  color: #c00;
}

#mocha .test pre.error {
  color: #c00;
}

#mocha .test pre {
  display: inline-block;
  font: 12px/1.5 monaco, monospace;
  margin: 5px;
  padding: 15px;
  border: 1px solid #eee;
  border-bottom-color: #ddd;
  -webkit-border-radius: 3px;
  -webkit-box-shadow: 0 1px 3px #eee;
}

#report.pass .test.fail {
  display: none;
}

#report.fail .test.pass {
  display: none;
}

#error {
  color: #c00;
  font-size: 1.5  em;
  font-weight: 100;
  letter-spacing: 1px;
}

#stats {
  position: fixed;
  top: 15px;
  right: 10px;
  font-size: 12px;
  margin: 0;
  color: #888;
}

#stats .progress {
  float: right;
  padding-top: 0;
}

#stats em {
  color: black;
}

#stats a {
  text-decoration: none;
  color: inherit;
}

#stats a:hover {
  border-bottom: 1px solid #eee;
}

#stats li {
  display: inline-block;
  margin: 0 5px;
  list-style: none;
  padding-top: 11px;
}

code .comment { color: #ddd }
code .init { color: #2F6FAD }
code .string { color: #5890AD }
code .keyword { color: #8A6343 }
code .number { color: #2F6FAD }
