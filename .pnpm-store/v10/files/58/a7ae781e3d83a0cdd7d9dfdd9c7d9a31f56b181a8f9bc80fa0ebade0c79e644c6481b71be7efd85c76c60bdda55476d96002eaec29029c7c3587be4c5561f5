Object.defineProperty(exports, '__esModule', { value: true });

const errors = require('./errors.js');

/**
 * @deprecated Use `registerSpanErrorInstrumentation()` instead. In v9, this function will be removed. Note that you don't need to call this in Node-based SDKs or when using `browserTracingIntegration`.
 */
function addTracingExtensions() {
  errors.registerSpanErrorInstrumentation();
}

exports.addTracingExtensions = addTracingExtensions;
//# sourceMappingURL=hubextensions.js.map
