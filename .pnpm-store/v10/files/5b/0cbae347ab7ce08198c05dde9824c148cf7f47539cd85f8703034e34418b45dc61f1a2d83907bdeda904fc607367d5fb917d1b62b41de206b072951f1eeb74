import s from"@csstools/postcss-progressive-custom-properties";import{tokenize as e,cloneTokens as r}from"@csstools/css-tokenizer";import{color as t,SyntaxFlag as o,serializeRGB as a,colorDataFitsRGB_Gamut as n,serializeP3 as l}from"@csstools/css-color-parser";import{replaceComponentValues as u,parseCommaSeparatedListOfComponentValues as i,isFunctionNode as p,stringify as c}from"@csstools/css-parser-algorithms";function hasFallback(s){const e=s.parent;if(!e)return!1;const r=s.prop.toLowerCase(),t=e.index(s);for(let s=0;s<t;s++){const t=e.nodes[s];if("decl"===t.type&&t.prop.toLowerCase()===r)return!0}return!1}function hasSupportsAtRuleAncestor(s){let e=s.parent;for(;e;)if("atrule"===e.type){if("supports"===e.name.toLowerCase()){if(-1!==e.params.toLowerCase().indexOf("lab("))return!0;if(-1!==e.params.toLowerCase().indexOf("lch("))return!0}e=e.parent}else e=e.parent;return!1}const f=/(lab|lch)\(/i,m=/^(lab|lch)$/i,basePlugin=s=>({postcssPlugin:"postcss-lab-function",Declaration:b=>{const g=b.value;if(!f.test(g))return;if(hasFallback(b))return;if(hasSupportsAtRuleAncestor(b))return;const y=e({css:g}),d=u(i(y),(s=>{if(p(s)&&m.test(s.getName())){const e=t(s);if(!e)return;if(e.syntaxFlags.has(o.HasNoneKeywords))return;if(e.syntaxFlags.has(o.RelativeColorSyntax))return;return a(e)}})),h=c(d);if(h===g)return;let v=h;null!=s&&s.subFeatures.displayP3&&(v=c(u(i(r(y)),(s=>{if(p(s)&&m.test(s.getName())){const e=t(s);if(!e)return;if(e.syntaxFlags.has(o.HasNoneKeywords))return;if(e.syntaxFlags.has(o.RelativeColorSyntax))return;return n(e)?a(e):l(e)}})))),b.cloneBefore({value:h}),null!=s&&s.subFeatures.displayP3&&v!==h&&b.cloneBefore({value:v}),null!=s&&s.preserve||b.remove()}});basePlugin.postcss=!0;const postcssPlugin=e=>{const r=Object.assign({enableProgressiveCustomProperties:!0,preserve:!1,subFeatures:{displayP3:!0}},e);return r.subFeatures=Object.assign({displayP3:!0},r.subFeatures),r.enableProgressiveCustomProperties&&(r.preserve||r.subFeatures.displayP3)?{postcssPlugin:"postcss-lab-function",plugins:[s(),basePlugin(r)]}:basePlugin(r)};postcssPlugin.postcss=!0;export{postcssPlugin as default};
